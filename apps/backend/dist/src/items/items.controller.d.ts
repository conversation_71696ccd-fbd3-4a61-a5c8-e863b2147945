import { ItemsService } from "./items.service";
import { CreateItemDto } from "./dto/create-item.dto";
import { UpdateItemDto } from "./dto/update-item.dto";
import { EnhancedUserPayload } from "../auth/types";
export declare class ItemsController {
    private readonly itemsService;
    constructor(itemsService: ItemsService);
    create(createItemDto: CreateItemDto, req: {
        user: EnhancedUserPayload;
    }): Promise<{
        name: string;
        id: string;
        status: string;
        createdAt: Date;
        updatedAt: Date;
        tenantId: string;
        sku: string | null;
        description: string | null;
        unitOfMeasure: string;
        defaultCost: number | null;
        lowStockThreshold: number | null;
    }>;
    findAll(req: {
        user: EnhancedUserPayload;
    }): Promise<{
        name: string;
        id: string;
        status: string;
        createdAt: Date;
        updatedAt: Date;
        tenantId: string;
        sku: string | null;
        description: string | null;
        unitOfMeasure: string;
        defaultCost: number | null;
        lowStockThreshold: number | null;
    }[]>;
    findOne(id: string, req: {
        user: EnhancedUserPayload;
    }): Promise<import("./items.service").ItemDetailResponse>;
    update(id: string, updateItemDto: UpdateItemDto, req: {
        user: EnhancedUserPayload;
    }): Promise<{
        name: string;
        id: string;
        status: string;
        createdAt: Date;
        updatedAt: Date;
        tenantId: string;
        sku: string | null;
        description: string | null;
        unitOfMeasure: string;
        defaultCost: number | null;
        lowStockThreshold: number | null;
    }>;
    remove(id: string, req: {
        user: EnhancedUserPayload;
    }): Promise<{
        name: string;
        id: string;
        status: string;
        createdAt: Date;
        updatedAt: Date;
        tenantId: string;
        sku: string | null;
        description: string | null;
        unitOfMeasure: string;
        defaultCost: number | null;
        lowStockThreshold: number | null;
    }>;
}
