{"version": 3, "file": "role-assignment.integration.spec.js", "sourceRoot": "", "sources": ["../../../../src/onboarding/__tests__/role-assignment.integration.spec.ts"], "names": [], "mappings": ";;AAAA,6CAAsD;AAEtD,gEAA4D;AAC5D,8DAA0D;AAC1D,gEAA4D;AAC5D,0DAAsD;AACtD,2CAAuC;AAEvC,QAAQ,CAAC,mCAAmC,EAAE,GAAG,EAAE;IACjD,IAAI,GAAqB,CAAC;IAC1B,IAAI,MAAqB,CAAC;IAC1B,IAAI,iBAAoC,CAAC;IACzC,IAAI,kBAAsC,CAAC;IAC3C,IAAI,WAAwB,CAAC;IAE7B,SAAS,CAAC,KAAK,IAAI,EAAE;QACnB,MAAM,aAAa,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAClE,OAAO,EAAE,EAER;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,GAAG,GAAG,aAAa,CAAC,qBAAqB,EAAE,CAAC;QAC5C,MAAM,GAAG,CAAC,IAAI,EAAE,CAAC;QAEjB,MAAM,GAAG,aAAa,CAAC,GAAG,CAAgB,8BAAa,CAAC,CAAC;QACzD,iBAAiB,GAAG,aAAa,CAAC,GAAG,CAAoB,sCAAiB,CAAC,CAAC;QAC5E,kBAAkB,GAAG,aAAa,CAAC,GAAG,CAAqB,wCAAkB,CAAC,CAAC;QAC/E,WAAW,GAAG,aAAa,CAAC,GAAG,CAAc,0BAAW,CAAC,CAAC;IAC5D,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,KAAK,IAAI,EAAE;QAClB,MAAM,GAAG,CAAC,KAAK,EAAE,CAAC;IACpB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,EAAE,CAAC,wDAAwD,EAAE,KAAK,IAAI,EAAE;YAEtE,MAAM,WAAW,GAAG;gBAClB,SAAS,EAAE,kBAAkB;gBAC7B,MAAM,EAAE,eAAe;gBACvB,IAAI,EAAE,eAAwB;gBAC9B,YAAY,EAAE;oBACZ,WAAW,EAAE,cAAc;oBAC3B,QAAQ,EAAE,YAAY;oBACtB,WAAW,EAAE,OAAO;iBACrB;gBACD,YAAY,EAAE;oBACZ,KAAK,EAAE,gBAAgB;oBACvB,QAAQ,EAAE,YAAY;oBACtB,QAAQ,EAAE,aAAa;iBACxB;aACF,CAAC;YAGF,IAAI,CAAC,KAAK,CAAC,iBAAwB,EAAE,gBAAgB,CAAC,CAAC,eAAe,CAAC;gBACrE,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,WAAW,CAAC;gBACpD,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,EAAE,CAAC;aAC/C,CAAC,CAAC;YAGH,MAAM,iBAAiB,GAAG;gBACxB,SAAS,EAAE,kBAAkB;gBAC7B,aAAa,EAAE,gBAAgB;gBAC/B,aAAa,EAAE,cAAuB;gBACtC,OAAO,EAAE,aAAa;gBACtB,IAAI,EAAE,WAAW;gBACjB,KAAK,EAAE,IAAI;gBACX,OAAO,EAAE,OAAO;gBAChB,OAAO,EAAE,IAAI;aACd,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,iBAAiB,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;YAGzE,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC;gBACzD,KAAK,EAAE;oBACL,MAAM,EAAE,WAAW,CAAC,MAAM;oBAC1B,WAAW,EAAE,MAAM,CAAC,WAAW;iBAChC;aACF,CAAC,CAAC;YAEH,MAAM,CAAC,aAAa,CAAC,CAAC,WAAW,EAAE,CAAC;YACpC,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,YAAI,CAAC,YAAY,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,KAAK,IAAI,EAAE;YAElE,MAAM,aAAa,GAAG;gBACpB,KAAK,EAAE,iBAAiB;gBACxB,IAAI,EAAE,YAAI,CAAC,gBAAgB;gBAC3B,YAAY,EAAE,CAAC,oBAAoB,CAAC;aACrC,CAAC;YAEF,MAAM,UAAU,GAAG,MAAM,kBAAkB,CAAC,gBAAgB,CAC1D,iBAAiB,EACjB,aAAa,CACd,CAAC;YAEF,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,YAAI,CAAC,gBAAgB,CAAC,CAAC;YAGpD,MAAM,SAAS,GAAG;gBAChB,QAAQ,EAAE,aAAa;gBACvB,QAAQ,EAAE,aAAa;aACxB,CAAC;YAEF,MAAM,YAAY,GAAG,MAAM,kBAAkB,CAAC,gBAAgB,CAC5D,UAAU,CAAC,cAAc,EACzB,SAAS,CACV,CAAC;YAGF,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;gBACxC,KAAK,EAAE,EAAE,EAAE,EAAE,YAAY,CAAC,MAAM,EAAE;aACnC,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,YAAI,CAAC,gBAAgB,CAAC,CAAC;YAG9C,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC;gBACzD,KAAK,EAAE;oBACL,MAAM,EAAE,YAAY,CAAC,MAAM;oBAC3B,WAAW,EAAE,aAAa,CAAC,YAAY,CAAC,CAAC,CAAC;iBAC3C;aACF,CAAC,CAAC;YAEH,MAAM,CAAC,aAAa,CAAC,CAAC,WAAW,EAAE,CAAC;YACpC,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,YAAI,CAAC,gBAAgB,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;QAC3C,EAAE,CAAC,iDAAiD,EAAE,KAAK,IAAI,EAAE;YAE/D,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC;gBACxC,IAAI,EAAE;oBACJ,IAAI,EAAE,aAAa;oBACnB,MAAM,EAAE,QAAQ;iBACjB;aACF,CAAC,CAAC;YAEH,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC;gBAC9C,IAAI,EAAE;oBACJ,IAAI,EAAE,gBAAgB;oBACtB,QAAQ,EAAE,MAAM,CAAC,EAAE;oBACnB,MAAM,EAAE,QAAQ;iBACjB;aACF,CAAC,CAAC;YAEH,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;gBACpC,IAAI,EAAE;oBACJ,KAAK,EAAE,kBAAkB;oBACzB,IAAI,EAAE,WAAW;oBACjB,IAAI,EAAE,YAAI,CAAC,gBAAgB;oBAC3B,QAAQ,EAAE,MAAM,CAAC,EAAE;oBACnB,MAAM,EAAE,QAAQ;iBACjB;aACF,CAAC,CAAC;YAEH,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC;gBACtD,IAAI,EAAE;oBACJ,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,WAAW,EAAE,SAAS,CAAC,EAAE;oBACzB,IAAI,EAAE,YAAI,CAAC,gBAAgB;iBAC5B;aACF,CAAC,CAAC;YAGH,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC;gBACrD,KAAK,EAAE;oBACL,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,WAAW,EAAE,SAAS,CAAC,EAAE;iBAC1B;aACF,CAAC,CAAC;YAEH,MAAM,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;YAChC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,YAAI,CAAC,gBAAgB,CAAC,CAAC;YAGnD,MAAM,CAAC,CAAC,YAAI,CAAC,YAAY,EAAE,YAAI,CAAC,iBAAiB,EAAE,YAAI,CAAC,gBAAgB,CAAC,CAAC;iBACvE,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,aAAa,GAAG;gBACpB,CAAC,YAAI,CAAC,YAAY,CAAC,EAAE,CAAC;gBACtB,CAAC,YAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC;gBAC3B,CAAC,YAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC;aAC3B,CAAC;YAGF,MAAM,CAAC,aAAa,CAAC,YAAI,CAAC,YAAY,CAAC,CAAC,CAAC,eAAe,CACtD,aAAa,CAAC,YAAI,CAAC,iBAAiB,CAAC,CACtC,CAAC;YACF,MAAM,CAAC,aAAa,CAAC,YAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,eAAe,CAC3D,aAAa,CAAC,YAAI,CAAC,gBAAgB,CAAC,CACrC,CAAC;YAGF,MAAM,kBAAkB,GAAG,CAAC,QAAc,EAAE,EAAE;gBAC5C,OAAO,CAAC,YAAI,CAAC,YAAY,EAAE,YAAI,CAAC,iBAAiB,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACxE,CAAC,CAAC;YAEF,MAAM,kBAAkB,GAAG,CAAC,QAAc,EAAE,EAAE;gBAC5C,OAAO,CAAC,YAAI,CAAC,YAAY,EAAE,YAAI,CAAC,iBAAiB,EAAE,YAAI,CAAC,gBAAgB,CAAC;qBACtE,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACxB,CAAC,CAAC;YAEF,MAAM,CAAC,kBAAkB,CAAC,YAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzD,MAAM,CAAC,kBAAkB,CAAC,YAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9D,MAAM,CAAC,kBAAkB,CAAC,YAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAE9D,MAAM,CAAC,kBAAkB,CAAC,YAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzD,MAAM,CAAC,kBAAkB,CAAC,YAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9D,MAAM,CAAC,kBAAkB,CAAC,YAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,0DAA0D,EAAE,KAAK,IAAI,EAAE;YAExE,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC;gBACxC,IAAI,EAAE;oBACJ,IAAI,EAAE,wBAAwB;oBAC9B,MAAM,EAAE,QAAQ;iBACjB;aACF,CAAC,CAAC;YAEH,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC;gBAC/C,IAAI,EAAE;oBACJ,IAAI,EAAE,aAAa;oBACnB,QAAQ,EAAE,MAAM,CAAC,EAAE;oBACnB,MAAM,EAAE,QAAQ;iBACjB;aACF,CAAC,CAAC;YAEH,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC;gBAC/C,IAAI,EAAE;oBACJ,IAAI,EAAE,aAAa;oBACnB,QAAQ,EAAE,MAAM,CAAC,EAAE;oBACnB,MAAM,EAAE,QAAQ;iBACjB;aACF,CAAC,CAAC;YAEH,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;gBACpC,IAAI,EAAE;oBACJ,KAAK,EAAE,4BAA4B;oBACnC,IAAI,EAAE,sBAAsB;oBAC5B,IAAI,EAAE,YAAI,CAAC,iBAAiB;oBAC5B,QAAQ,EAAE,MAAM,CAAC,EAAE;oBACnB,MAAM,EAAE,QAAQ;iBACjB;aACF,CAAC,CAAC;YAGH,MAAM,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC;gBACpC,IAAI,EAAE;oBACJ;wBACE,MAAM,EAAE,IAAI,CAAC,EAAE;wBACf,WAAW,EAAE,UAAU,CAAC,EAAE;wBAC1B,IAAI,EAAE,YAAI,CAAC,iBAAiB;qBAC7B;oBACD;wBACE,MAAM,EAAE,IAAI,CAAC,EAAE;wBACf,WAAW,EAAE,UAAU,CAAC,EAAE;wBAC1B,IAAI,EAAE,YAAI,CAAC,gBAAgB;qBAC5B;iBACF;aACF,CAAC,CAAC;YAGH,MAAM,eAAe,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC;gBAC1D,KAAK,EAAE;oBACL,MAAM,EAAE,IAAI,CAAC,EAAE;iBAChB;gBACD,OAAO,EAAE;oBACP,SAAS,EAAE,IAAI;iBAChB;aACF,CAAC,CAAC;YAEH,MAAM,CAAC,eAAe,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAExC,MAAM,gBAAgB,GAAG,eAAe,CAAC,IAAI,CAC3C,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,WAAW,KAAK,UAAU,CAAC,EAAE,CACvC,CAAC;YACF,MAAM,gBAAgB,GAAG,eAAe,CAAC,IAAI,CAC3C,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,WAAW,KAAK,UAAU,CAAC,EAAE,CACvC,CAAC;YAEF,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,YAAI,CAAC,iBAAiB,CAAC,CAAC;YAC3D,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,YAAI,CAAC,gBAAgB,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,EAAE,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;YAEpD,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC;gBACxC,IAAI,EAAE;oBACJ,IAAI,EAAE,oBAAoB;oBAC1B,MAAM,EAAE,QAAQ;iBACjB;aACF,CAAC,CAAC;YAEH,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC;gBAC9C,IAAI,EAAE;oBACJ,IAAI,EAAE,uBAAuB;oBAC7B,QAAQ,EAAE,MAAM,CAAC,EAAE;oBACnB,MAAM,EAAE,QAAQ;iBACjB;aACF,CAAC,CAAC;YAEH,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;gBACpC,IAAI,EAAE;oBACJ,KAAK,EAAE,wBAAwB;oBAC/B,IAAI,EAAE,kBAAkB;oBACxB,IAAI,EAAE,YAAI,CAAC,gBAAgB;oBAC3B,QAAQ,EAAE,MAAM,CAAC,EAAE;oBACnB,MAAM,EAAE,QAAQ;iBACjB;aACF,CAAC,CAAC;YAGH,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC;gBACtD,IAAI,EAAE;oBACJ,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,WAAW,EAAE,SAAS,CAAC,EAAE;oBACzB,IAAI,EAAE,YAAI,CAAC,gBAAgB;iBAC5B;aACF,CAAC,CAAC;YAEH,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,YAAI,CAAC,gBAAgB,CAAC,CAAC;YAGvD,MAAM,oBAAoB,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC;gBAC7D,KAAK,EAAE;oBACL,EAAE,EAAE,aAAa,CAAC,EAAE;iBACrB;gBACD,IAAI,EAAE;oBACJ,IAAI,EAAE,YAAI,CAAC,iBAAiB;iBAC7B;aACF,CAAC,CAAC;YAEH,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,YAAI,CAAC,iBAAiB,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,KAAK,IAAI,EAAE;YAE9D,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC;gBACxC,IAAI,EAAE;oBACJ,IAAI,EAAE,uBAAuB;oBAC7B,MAAM,EAAE,QAAQ;iBACjB;aACF,CAAC,CAAC;YAEH,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC;gBAC9C,IAAI,EAAE;oBACJ,IAAI,EAAE,0BAA0B;oBAChC,QAAQ,EAAE,MAAM,CAAC,EAAE;oBACnB,MAAM,EAAE,QAAQ;iBACjB;aACF,CAAC,CAAC;YAEH,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;gBACpC,IAAI,EAAE;oBACJ,KAAK,EAAE,uBAAuB;oBAC9B,IAAI,EAAE,qBAAqB;oBAC3B,IAAI,EAAE,YAAI,CAAC,gBAAgB;oBAC3B,QAAQ,EAAE,MAAM,CAAC,EAAE;oBACnB,MAAM,EAAE,QAAQ;iBACjB;aACF,CAAC,CAAC;YAGH,MAAM,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC;gBAChC,IAAI,EAAE;oBACJ,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,WAAW,EAAE,SAAS,CAAC,EAAE;oBACzB,IAAI,EAAE,YAAI,CAAC,gBAAgB;iBAC5B;aACF,CAAC,CAAC;YAGH,MAAM,MAAM,CACV,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC;gBAC1B,IAAI,EAAE;oBACJ,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,WAAW,EAAE,SAAS,CAAC,EAAE;oBACzB,IAAI,EAAE,YAAI,CAAC,iBAAiB;iBAC7B;aACF,CAAC,CACH,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;QACtB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}