import { OnboardingService } from "./onboarding.service";
import { EnhancedUserPayload } from "../auth/types";
import { StartBusinessOnboardingDto, CreateAdminAccountDto, SetupWarehouseDto, CompleteOnboardingDto, StartBusinessOnboardingResponseDto, CreateAdminAccountResponseDto, SetupWarehouseResponseDto, CompleteOnboardingResponseDto } from "./dto";
export declare class OnboardingController {
    private readonly onboardingService;
    constructor(onboardingService: OnboardingService);
    startBusinessOnboarding(dto: StartBusinessOnboardingDto): Promise<StartBusinessOnboardingResponseDto>;
    createAdminAccount(dto: CreateAdminAccountDto): Promise<CreateAdminAccountResponseDto>;
    setupWarehouse(dto: SetupWarehouseDto): Promise<SetupWarehouseResponseDto>;
    completeOnboarding(dto: CompleteOnboardingDto): Promise<CompleteOnboardingResponseDto>;
    completeProfile(req: {
        user: EnhancedUserPayload;
    }, completeProfileDto: any): Promise<{
        name: string | null;
        id: string;
        email: string;
        authUserId: string | null;
        role: import("@prisma/client").$Enums.Role;
        status: string;
        createdAt: Date;
        updatedAt: Date;
        tenantId: string | null;
    }>;
}
