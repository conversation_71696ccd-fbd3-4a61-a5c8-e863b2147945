import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { OnboardingService } from '../onboarding.service';
import { InvitationsService } from '../invitations.service';
import { AuthService } from '../../auth/auth.service';
import { Role } from '@quildora/types';

describe('Role Assignment Integration Tests', () => {
  let app: INestApplication;
  let prisma: PrismaService;
  let onboardingService: OnboardingService;
  let invitationsService: InvitationsService;
  let authService: AuthService;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        // Import your actual modules here
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    prisma = moduleFixture.get<PrismaService>(PrismaService);
    onboardingService = moduleFixture.get<OnboardingService>(OnboardingService);
    invitationsService = moduleFixture.get<InvitationsService>(InvitationsService);
    authService = moduleFixture.get<AuthService>(AuthService);
  });

  afterAll(async () => {
    await app.close();
  });

  describe('Onboarding Role Assignment', () => {
    it('should assign TENANT_ADMIN role during warehouse setup', async () => {
      // Create a test onboarding session
      const sessionData = {
        sessionId: 'test-session-123',
        userId: 'test-user-123',
        step: 'admin_account' as const,
        businessInfo: {
          companyName: 'Test Company',
          industry: 'Technology',
          companySize: '10-50',
        },
        adminAccount: {
          email: '<EMAIL>',
          fullName: 'Test Admin',
          password: 'password123',
        },
      };

      // Mock the session service
      jest.spyOn(onboardingService as any, 'sessionService').mockReturnValue({
        getSession: jest.fn().mockResolvedValue(sessionData),
        updateSession: jest.fn().mockResolvedValue({}),
      });

      // Test warehouse setup
      const warehouseSetupDto = {
        sessionId: 'test-session-123',
        warehouseName: 'Test Warehouse',
        warehouseType: 'DISTRIBUTION' as const,
        address: '123 Test St',
        city: 'Test City',
        state: 'TS',
        zipCode: '12345',
        country: 'US',
      };

      const result = await onboardingService.setupWarehouse(warehouseSetupDto);

      // Verify warehouse user was created with TENANT_ADMIN role
      const warehouseUser = await prisma.warehouseUser.findFirst({
        where: {
          userId: sessionData.userId,
          warehouseId: result.warehouseId,
        },
      });

      expect(warehouseUser).toBeDefined();
      expect(warehouseUser.role).toBe(Role.TENANT_ADMIN);
    });

    it('should assign correct roles during team invitation', async () => {
      // Create test invitation
      const invitationDto = {
        email: '<EMAIL>',
        role: Role.WAREHOUSE_MEMBER,
        warehouseIds: ['test-warehouse-123'],
      };

      const invitation = await invitationsService.createInvitation(
        'test-admin-user',
        invitationDto
      );

      expect(invitation.role).toBe(Role.WAREHOUSE_MEMBER);

      // Test invitation acceptance
      const acceptDto = {
        fullName: 'Test Member',
        password: 'password123',
      };

      const acceptResult = await invitationsService.acceptInvitation(
        invitation.invitationCode,
        acceptDto
      );

      // Verify user was created with correct role
      const user = await prisma.user.findUnique({
        where: { id: acceptResult.userId },
      });

      expect(user.role).toBe(Role.WAREHOUSE_MEMBER);

      // Verify warehouse assignment
      const warehouseUser = await prisma.warehouseUser.findFirst({
        where: {
          userId: acceptResult.userId,
          warehouseId: invitationDto.warehouseIds[0],
        },
      });

      expect(warehouseUser).toBeDefined();
      expect(warehouseUser.role).toBe(Role.WAREHOUSE_MEMBER);
    });
  });

  describe('Warehouse Access Validation', () => {
    it('should validate user warehouse access correctly', async () => {
      // Create test data
      const tenant = await prisma.tenant.create({
        data: {
          name: 'Test Tenant',
          status: 'ACTIVE',
        },
      });

      const warehouse = await prisma.warehouse.create({
        data: {
          name: 'Test Warehouse',
          tenantId: tenant.id,
          status: 'Active',
        },
      });

      const user = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Test User',
          role: Role.WAREHOUSE_MEMBER,
          tenantId: tenant.id,
          status: 'Active',
        },
      });

      const warehouseUser = await prisma.warehouseUser.create({
        data: {
          userId: user.id,
          warehouseId: warehouse.id,
          role: Role.WAREHOUSE_MEMBER,
        },
      });

      // Test access validation
      const hasAccess = await prisma.warehouseUser.findFirst({
        where: {
          userId: user.id,
          warehouseId: warehouse.id,
        },
      });

      expect(hasAccess).toBeDefined();
      expect(hasAccess.role).toBe(Role.WAREHOUSE_MEMBER);

      // Test role hierarchy
      expect([Role.TENANT_ADMIN, Role.WAREHOUSE_MANAGER, Role.WAREHOUSE_MEMBER])
        .toContain(hasAccess.role);
    });

    it('should enforce role hierarchy correctly', async () => {
      const roleHierarchy = {
        [Role.TENANT_ADMIN]: 3,
        [Role.WAREHOUSE_MANAGER]: 2,
        [Role.WAREHOUSE_MEMBER]: 1,
      };

      // Test that TENANT_ADMIN has highest privileges
      expect(roleHierarchy[Role.TENANT_ADMIN]).toBeGreaterThan(
        roleHierarchy[Role.WAREHOUSE_MANAGER]
      );
      expect(roleHierarchy[Role.WAREHOUSE_MANAGER]).toBeGreaterThan(
        roleHierarchy[Role.WAREHOUSE_MEMBER]
      );

      // Test role permissions
      const canManageWarehouse = (userRole: Role) => {
        return [Role.TENANT_ADMIN, Role.WAREHOUSE_MANAGER].includes(userRole);
      };

      const canAccessWarehouse = (userRole: Role) => {
        return [Role.TENANT_ADMIN, Role.WAREHOUSE_MANAGER, Role.WAREHOUSE_MEMBER]
          .includes(userRole);
      };

      expect(canManageWarehouse(Role.TENANT_ADMIN)).toBe(true);
      expect(canManageWarehouse(Role.WAREHOUSE_MANAGER)).toBe(true);
      expect(canManageWarehouse(Role.WAREHOUSE_MEMBER)).toBe(false);

      expect(canAccessWarehouse(Role.TENANT_ADMIN)).toBe(true);
      expect(canAccessWarehouse(Role.WAREHOUSE_MANAGER)).toBe(true);
      expect(canAccessWarehouse(Role.WAREHOUSE_MEMBER)).toBe(true);
    });
  });

  describe('Multi-Warehouse Access', () => {
    it('should handle multi-warehouse role assignments correctly', async () => {
      // Create test data
      const tenant = await prisma.tenant.create({
        data: {
          name: 'Multi-Warehouse Tenant',
          status: 'ACTIVE',
        },
      });

      const warehouse1 = await prisma.warehouse.create({
        data: {
          name: 'Warehouse 1',
          tenantId: tenant.id,
          status: 'Active',
        },
      });

      const warehouse2 = await prisma.warehouse.create({
        data: {
          name: 'Warehouse 2',
          tenantId: tenant.id,
          status: 'Active',
        },
      });

      const user = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Multi Warehouse User',
          role: Role.WAREHOUSE_MANAGER,
          tenantId: tenant.id,
          status: 'Active',
        },
      });

      // Assign user to multiple warehouses with different roles
      await prisma.warehouseUser.createMany({
        data: [
          {
            userId: user.id,
            warehouseId: warehouse1.id,
            role: Role.WAREHOUSE_MANAGER,
          },
          {
            userId: user.id,
            warehouseId: warehouse2.id,
            role: Role.WAREHOUSE_MEMBER,
          },
        ],
      });

      // Verify multi-warehouse access
      const warehouseAccess = await prisma.warehouseUser.findMany({
        where: {
          userId: user.id,
        },
        include: {
          warehouse: true,
        },
      });

      expect(warehouseAccess).toHaveLength(2);
      
      const warehouse1Access = warehouseAccess.find(
        wa => wa.warehouseId === warehouse1.id
      );
      const warehouse2Access = warehouseAccess.find(
        wa => wa.warehouseId === warehouse2.id
      );

      expect(warehouse1Access.role).toBe(Role.WAREHOUSE_MANAGER);
      expect(warehouse2Access.role).toBe(Role.WAREHOUSE_MEMBER);
    });
  });

  describe('Role Assignment Edge Cases', () => {
    it('should handle role updates correctly', async () => {
      // Create test user and warehouse
      const tenant = await prisma.tenant.create({
        data: {
          name: 'Role Update Tenant',
          status: 'ACTIVE',
        },
      });

      const warehouse = await prisma.warehouse.create({
        data: {
          name: 'Role Update Warehouse',
          tenantId: tenant.id,
          status: 'Active',
        },
      });

      const user = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Role Update User',
          role: Role.WAREHOUSE_MEMBER,
          tenantId: tenant.id,
          status: 'Active',
        },
      });

      // Initial role assignment
      const warehouseUser = await prisma.warehouseUser.create({
        data: {
          userId: user.id,
          warehouseId: warehouse.id,
          role: Role.WAREHOUSE_MEMBER,
        },
      });

      expect(warehouseUser.role).toBe(Role.WAREHOUSE_MEMBER);

      // Update role
      const updatedWarehouseUser = await prisma.warehouseUser.update({
        where: {
          id: warehouseUser.id,
        },
        data: {
          role: Role.WAREHOUSE_MANAGER,
        },
      });

      expect(updatedWarehouseUser.role).toBe(Role.WAREHOUSE_MANAGER);
    });

    it('should prevent duplicate warehouse assignments', async () => {
      // Test that unique constraint works
      const tenant = await prisma.tenant.create({
        data: {
          name: 'Duplicate Test Tenant',
          status: 'ACTIVE',
        },
      });

      const warehouse = await prisma.warehouse.create({
        data: {
          name: 'Duplicate Test Warehouse',
          tenantId: tenant.id,
          status: 'Active',
        },
      });

      const user = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Duplicate Test User',
          role: Role.WAREHOUSE_MEMBER,
          tenantId: tenant.id,
          status: 'Active',
        },
      });

      // First assignment should succeed
      await prisma.warehouseUser.create({
        data: {
          userId: user.id,
          warehouseId: warehouse.id,
          role: Role.WAREHOUSE_MEMBER,
        },
      });

      // Second assignment should fail due to unique constraint
      await expect(
        prisma.warehouseUser.create({
          data: {
            userId: user.id,
            warehouseId: warehouse.id,
            role: Role.WAREHOUSE_MANAGER,
          },
        })
      ).rejects.toThrow();
    });
  });
});
