/**
 * Performance monitoring utilities for Quildora
 * Tracks query performance, component render times, and user interactions
 */

import React from "react";

interface PerformanceMetric {
  name: string;
  duration: number;
  timestamp: number;
  type: "query" | "render" | "interaction" | "navigation";
  metadata?: Record<string, any>;
}

interface PerformanceThresholds {
  query: number;
  render: number;
  interaction: number;
  navigation: number;
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private thresholds: PerformanceThresholds = {
    query: 1000, // 1 second for API queries
    render: 16, // 16ms for 60fps
    interaction: 100, // 100ms for user interactions
    navigation: 500, // 500ms for navigation
  };
  private isEnabled = process.env.NODE_ENV === "development";

  /**
   * Start timing a performance metric
   */
  startTiming(
    name: string,
    type: PerformanceMetric["type"],
    metadata?: Record<string, any>
  ) {
    if (!this.isEnabled) return null;

    const startTime = performance.now();
    return {
      end: () => {
        const duration = performance.now() - startTime;
        this.recordMetric({
          name,
          duration,
          timestamp: Date.now(),
          type,
          metadata,
        });
        return duration;
      },
    };
  }

  /**
   * Record a performance metric
   */
  private recordMetric(metric: PerformanceMetric) {
    this.metrics.push(metric);

    // Check if metric exceeds threshold
    if (metric.duration > this.thresholds[metric.type]) {
      console.warn(
        `🐌 Slow ${metric.type}: ${metric.name} took ${metric.duration.toFixed(
          2
        )}ms`,
        {
          threshold: this.thresholds[metric.type],
          metadata: metric.metadata,
        }
      );
    }

    // Keep only last 100 metrics to prevent memory leaks
    if (this.metrics.length > 100) {
      this.metrics = this.metrics.slice(-100);
    }
  }

  /**
   * Get performance summary
   */
  getSummary() {
    if (!this.isEnabled) return null;

    const summary = {
      totalMetrics: this.metrics.length,
      averages: {} as Record<string, number>,
      slowest: {} as Record<string, PerformanceMetric[]>,
      violations: 0,
    };

    // Calculate averages by type
    const typeGroups = this.groupBy(this.metrics, "type");
    Object.entries(typeGroups).forEach(([type, metrics]) => {
      const avg =
        metrics.reduce((sum, m) => sum + m.duration, 0) / metrics.length;
      summary.averages[type] = Math.round(avg * 100) / 100;
    });

    // Find slowest operations by type
    Object.entries(typeGroups).forEach(([type, metrics]) => {
      summary.slowest[type] = metrics
        .sort((a, b) => b.duration - a.duration)
        .slice(0, 3);
    });

    // Count threshold violations
    summary.violations = this.metrics.filter(
      (m) => m.duration > this.thresholds[m.type]
    ).length;

    return summary;
  }

  /**
   * Clear all metrics
   */
  clear() {
    this.metrics = [];
  }

  /**
   * Update performance thresholds
   */
  setThresholds(thresholds: Partial<PerformanceThresholds>) {
    this.thresholds = { ...this.thresholds, ...thresholds };
  }

  /**
   * Group array by property
   */
  private groupBy<T>(array: T[], key: keyof T): Record<string, T[]> {
    return array.reduce((groups, item) => {
      const group = String(item[key]);
      groups[group] = groups[group] || [];
      groups[group].push(item);
      return groups;
    }, {} as Record<string, T[]>);
  }
}

// Global performance monitor instance
export const performanceMonitor = new PerformanceMonitor();

/**
 * React hook for monitoring component render performance
 */
export function useRenderPerformance(componentName: string) {
  if (process.env.NODE_ENV !== "development") return;

  const timer = performanceMonitor.startTiming(
    `${componentName} render`,
    "render"
  );

  // End timing on unmount
  React.useEffect(() => {
    return () => {
      timer?.end();
    };
  }, [timer]);
}

/**
 * Higher-order component for monitoring render performance
 */
export function withPerformanceMonitoring<P extends object>(
  Component: React.ComponentType<P>,
  componentName?: string
) {
  const name =
    componentName || Component.displayName || Component.name || "Unknown";

  return function PerformanceMonitoredComponent(props: P) {
    useRenderPerformance(name);
    return <Component {...props} />;
  };
}

/**
 * Utility for monitoring query performance
 */
export function monitorQuery<T>(
  queryName: string,
  queryFn: () => Promise<T>,
  metadata?: Record<string, any>
): Promise<T> {
  const timer = performanceMonitor.startTiming(queryName, "query", metadata);

  return queryFn()
    .then((result) => {
      timer?.end();
      return result;
    })
    .catch((error) => {
      timer?.end();
      throw error;
    });
}

/**
 * Utility for monitoring user interactions
 */
export function monitorInteraction(
  interactionName: string,
  interactionFn: () => void | Promise<void>,
  metadata?: Record<string, any>
) {
  const timer = performanceMonitor.startTiming(
    interactionName,
    "interaction",
    metadata
  );

  const result = interactionFn();

  if (result instanceof Promise) {
    return result.finally(() => timer?.end());
  } else {
    timer?.end();
    return result;
  }
}

/**
 * Utility for monitoring navigation performance
 */
export function monitorNavigation(
  routeName: string,
  metadata?: Record<string, any>
) {
  return performanceMonitor.startTiming(
    `Navigate to ${routeName}`,
    "navigation",
    metadata
  );
}

/**
 * Performance debugging utilities
 */
export const performanceDebug = {
  /**
   * Log current performance summary to console
   */
  logSummary() {
    const summary = performanceMonitor.getSummary();
    if (summary) {
      console.group("🚀 Performance Summary");
      console.log("Total metrics:", summary.totalMetrics);
      console.log("Averages by type:", summary.averages);
      console.log("Threshold violations:", summary.violations);
      console.log("Slowest operations:", summary.slowest);
      console.groupEnd();
    }
  },

  /**
   * Start monitoring all React Query operations
   */
  enableQueryMonitoring() {
    if (typeof window !== "undefined") {
      // Monitor fetch requests
      const originalFetch = window.fetch;
      window.fetch = function (...args) {
        const url = args[0]?.toString() || "unknown";
        const timer = performanceMonitor.startTiming(`Fetch: ${url}`, "query", {
          url,
        });

        return originalFetch
          .apply(this, args)
          .then((response) => {
            timer?.end();
            return response;
          })
          .catch((error) => {
            timer?.end();
            throw error;
          });
      };
    }
  },

  /**
   * Monitor warehouse context switches
   */
  monitorWarehouseSwitch(fromWarehouse: string, toWarehouse: string) {
    return performanceMonitor.startTiming("Warehouse switch", "navigation", {
      from: fromWarehouse,
      to: toWarehouse,
    });
  },

  /**
   * Monitor pallet operations
   */
  monitorPalletOperation(operation: string, palletCount: number = 1) {
    return performanceMonitor.startTiming(
      `Pallet ${operation}`,
      "interaction",
      { operation, palletCount }
    );
  },
};

// Auto-enable query monitoring in development
if (process.env.NODE_ENV === "development" && typeof window !== "undefined") {
  performanceDebug.enableQueryMonitoring();
}

// Export performance monitor for global access
if (typeof window !== "undefined") {
  (window as any).__quildoraPerformance = {
    monitor: performanceMonitor,
    debug: performanceDebug,
  };
}
