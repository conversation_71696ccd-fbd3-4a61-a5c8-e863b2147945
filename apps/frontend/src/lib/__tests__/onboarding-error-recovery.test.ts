import { describe, it, expect, vi, beforeEach } from 'vitest';
import { 
  OnboardingErrorRecovery, 
  OnboardingErrorType,
  OnboardingError 
} from '../onboarding-error-recovery';

describe('OnboardingErrorRecovery', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Clear any stored errors
    (OnboardingErrorRecovery as any).errors = [];
  });

  describe('logError', () => {
    it('should log an error', () => {
      const error: OnboardingError = {
        type: OnboardingErrorType.NETWORK_ERROR,
        message: 'Network error occurred',
        step: 'business_info',
        retryable: true,
        timestamp: Date.now(),
      };

      OnboardingErrorRecovery.logError(error);

      expect((OnboardingErrorRecovery as any).errors).toHaveLength(1);
      expect((OnboardingErrorRecovery as any).errors[0]).toEqual(error);
    });

    it('should limit the number of stored errors', () => {
      // Add max errors + 1
      const maxErrors = (OnboardingErrorRecovery as any).MAX_STORED_ERRORS;
      
      for (let i = 0; i < maxErrors + 5; i++) {
        OnboardingErrorRecovery.logError({
          type: OnboardingErrorType.NETWORK_ERROR,
          message: `Error ${i}`,
          step: 'business_info',
          retryable: true,
          timestamp: Date.now() + i,
        });
      }

      expect((OnboardingErrorRecovery as any).errors).toHaveLength(maxErrors);
      // Should keep the most recent errors
      expect((OnboardingErrorRecovery as any).errors[0].message).toBe(`Error ${5}`);
    });
  });

  describe('getRecoveryActions', () => {
    it('should return retry action for network errors', () => {
      const error: OnboardingError = {
        type: OnboardingErrorType.NETWORK_ERROR,
        message: 'Network error occurred',
        step: 'business_info',
        retryable: true,
        timestamp: Date.now(),
      };

      const mockContext = {
        retryCallback: vi.fn(),
        reloadCallback: vi.fn(),
        restartCallback: vi.fn(),
      };

      const actions = OnboardingErrorRecovery.getRecoveryActions(error, mockContext);

      expect(actions).toHaveLength(2);
      expect(actions[0].strategy).toBe('RETRY');
      expect(actions[1].strategy).toBe('RELOAD');
      
      // Execute the actions
      actions[0].action();
      expect(mockContext.retryCallback).toHaveBeenCalled();
      
      actions[1].action();
      expect(mockContext.reloadCallback).toHaveBeenCalled();
    });

    it('should return restart action for session expired errors', () => {
      const error: OnboardingError = {
        type: OnboardingErrorType.SESSION_EXPIRED,
        message: 'Session expired',
        step: 'admin_account',
        retryable: false,
        timestamp: Date.now(),
      };

      const mockContext = {
        retryCallback: vi.fn(),
        reloadCallback: vi.fn(),
        restartCallback: vi.fn(),
      };

      const actions = OnboardingErrorRecovery.getRecoveryActions(error, mockContext);

      expect(actions).toHaveLength(1);
      expect(actions[0].strategy).toBe('RESTART');
      
      // Execute the action
      actions[0].action();
      expect(mockContext.restartCallback).toHaveBeenCalled();
    });

    it('should return appropriate actions for validation errors', () => {
      const error: OnboardingError = {
        type: OnboardingErrorType.VALIDATION_ERROR,
        message: 'Invalid input',
        step: 'warehouse_setup',
        field: 'warehouseName',
        retryable: false,
        timestamp: Date.now(),
      };

      const mockContext = {
        retryCallback: vi.fn(),
        reloadCallback: vi.fn(),
        restartCallback: vi.fn(),
      };

      const actions = OnboardingErrorRecovery.getRecoveryActions(error, mockContext);

      expect(actions).toHaveLength(1);
      expect(actions[0].strategy).toBe('EDIT');
      expect(actions[0].field).toBe('warehouseName');
    });

    it('should return default actions when no context provided', () => {
      const error: OnboardingError = {
        type: OnboardingErrorType.NETWORK_ERROR,
        message: 'Network error occurred',
        step: 'business_info',
        retryable: true,
        timestamp: Date.now(),
      };

      const actions = OnboardingErrorRecovery.getRecoveryActions(error);

      expect(actions).toHaveLength(2);
      expect(actions[0].strategy).toBe('RETRY');
      expect(actions[1].strategy).toBe('RELOAD');
      
      // Actions should be no-ops without context
      actions[0].action();
      actions[1].action();
    });
  });

  describe('getErrorSummary', () => {
    it('should return error summary', () => {
      // Log multiple errors
      OnboardingErrorRecovery.logError({
        type: OnboardingErrorType.NETWORK_ERROR,
        message: 'Network error 1',
        step: 'business_info',
        retryable: true,
        timestamp: Date.now() - 5000,
      });
      
      OnboardingErrorRecovery.logError({
        type: OnboardingErrorType.VALIDATION_ERROR,
        message: 'Invalid input',
        step: 'admin_account',
        field: 'email',
        retryable: false,
        timestamp: Date.now() - 3000,
      });
      
      OnboardingErrorRecovery.logError({
        type: OnboardingErrorType.SERVER_ERROR,
        message: 'Server error',
        step: 'warehouse_setup',
        retryable: true,
        timestamp: Date.now() - 1000,
      });

      const summary = OnboardingErrorRecovery.getErrorSummary();

      expect(summary.totalErrors).toBe(3);
      expect(summary.byType[OnboardingErrorType.NETWORK_ERROR]).toBe(1);
      expect(summary.byType[OnboardingErrorType.VALIDATION_ERROR]).toBe(1);
      expect(summary.byType[OnboardingErrorType.SERVER_ERROR]).toBe(1);
      expect(summary.byStep.business_info).toBe(1);
      expect(summary.byStep.admin_account).toBe(1);
      expect(summary.byStep.warehouse_setup).toBe(1);
      expect(summary.retryableCount).toBe(2);
      expect(summary.mostRecentError.message).toBe('Server error');
    });

    it('should return empty summary when no errors', () => {
      const summary = OnboardingErrorRecovery.getErrorSummary();

      expect(summary.totalErrors).toBe(0);
      expect(Object.keys(summary.byType).length).toBe(0);
      expect(Object.keys(summary.byStep).length).toBe(0);
      expect(summary.retryableCount).toBe(0);
      expect(summary.mostRecentError).toBeNull();
    });
  });

  describe('clearErrors', () => {
    it('should clear all errors', () => {
      // Log some errors
      OnboardingErrorRecovery.logError({
        type: OnboardingErrorType.NETWORK_ERROR,
        message: 'Network error',
        step: 'business_info',
        retryable: true,
        timestamp: Date.now(),
      });
      
      OnboardingErrorRecovery.logError({
        type: OnboardingErrorType.VALIDATION_ERROR,
        message: 'Invalid input',
        step: 'admin_account',
        field: 'email',
        retryable: false,
        timestamp: Date.now(),
      });

      expect((OnboardingErrorRecovery as any).errors).toHaveLength(2);
      
      OnboardingErrorRecovery.clearErrors();
      
      expect((OnboardingErrorRecovery as any).errors).toHaveLength(0);
    });
  });

  describe('getErrorsByStep', () => {
    it('should return errors for a specific step', () => {
      // Log errors for different steps
      OnboardingErrorRecovery.logError({
        type: OnboardingErrorType.NETWORK_ERROR,
        message: 'Network error 1',
        step: 'business_info',
        retryable: true,
        timestamp: Date.now() - 5000,
      });
      
      OnboardingErrorRecovery.logError({
        type: OnboardingErrorType.VALIDATION_ERROR,
        message: 'Invalid input',
        step: 'admin_account',
        field: 'email',
        retryable: false,
        timestamp: Date.now() - 3000,
      });
      
      OnboardingErrorRecovery.logError({
        type: OnboardingErrorType.SERVER_ERROR,
        message: 'Server error',
        step: 'business_info',
        retryable: true,
        timestamp: Date.now() - 1000,
      });

      const businessInfoErrors = OnboardingErrorRecovery.getErrorsByStep('business_info');
      const adminAccountErrors = OnboardingErrorRecovery.getErrorsByStep('admin_account');
      const warehouseSetupErrors = OnboardingErrorRecovery.getErrorsByStep('warehouse_setup');

      expect(businessInfoErrors).toHaveLength(2);
      expect(adminAccountErrors).toHaveLength(1);
      expect(warehouseSetupErrors).toHaveLength(0);
    });
  });
});
