'use client';

import React from 'react';
import { OnboardingErrorBoundary } from '@/components/onboarding/OnboardingErrorBoundary';
import { OnboardingProvider } from '@/components/providers/onboarding-provider';

export default function OnboardingSignupLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <OnboardingErrorBoundary
      onError={(error, errorInfo) => {
        // Log to monitoring service
        console.error('Onboarding signup error:', error, errorInfo);
        
        // Report to analytics
        if (typeof window !== 'undefined' && window.gtag) {
          window.gtag('event', 'exception', {
            description: `Onboarding Error: ${error.toString()}`,
            fatal: false,
          });
        }
      }}
    >
      <OnboardingProvider>
        {children}
      </OnboardingProvider>
    </OnboardingErrorBoundary>
  );
}
