"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { toast } from "sonner";

import OnboardingLayout from "@/components/layout/OnboardingLayout";
import OnboardingFormWrapper, {
  OnboardingField,
} from "@/components/onboarding/OnboardingFormWrapper";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useOnboarding } from "@/components/providers/onboarding-provider";

// Validation schema
const warehouseSetupSchema = z.object({
  warehouseName: z
    .string()
    .min(2, "Warehouse name must be at least 2 characters")
    .max(100, "Warehouse name must be less than 100 characters")
    .regex(
      /^[a-zA-Z0-9\s\-_]+$/,
      "Warehouse name can only contain letters, numbers, spaces, hyphens, and underscores"
    ),

  address: z
    .string()
    .max(500, "Address must be less than 500 characters")
    .optional(),

  warehouseType: z.enum(
    ["distribution", "manufacturing", "retail", "storage", "other"],
    {
      required_error: "Please select a warehouse type",
    }
  ),

  expectedVolume: z.enum(["small", "medium", "large", "enterprise"], {
    required_error: "Please select expected volume",
  }),

  description: z
    .string()
    .max(1000, "Description must be less than 1000 characters")
    .optional(),
});

type WarehouseSetupForm = z.infer<typeof warehouseSetupSchema>;

const WAREHOUSE_TYPES = [
  {
    value: "distribution" as const,
    label: "Distribution Center",
    description:
      "Receiving, storing, and shipping products to customers or retailers",
  },
  {
    value: "manufacturing" as const,
    label: "Manufacturing Facility",
    description:
      "Production facility with raw materials and finished goods storage",
  },
  {
    value: "retail" as const,
    label: "Retail Store",
    description: "Store with inventory for direct customer sales",
  },
  {
    value: "storage" as const,
    label: "Storage Facility",
    description: "Long-term storage of goods and materials",
  },
  {
    value: "other" as const,
    label: "Other",
    description: "Custom warehouse type not listed above",
  },
];

const VOLUME_OPTIONS = [
  {
    value: "small" as const,
    label: "Small (1-1,000 items)",
    description: "Perfect for small businesses and startups",
  },
  {
    value: "medium" as const,
    label: "Medium (1,000-10,000 items)",
    description: "Growing businesses with moderate inventory",
  },
  {
    value: "large" as const,
    label: "Large (10,000-100,000 items)",
    description: "Established businesses with high volume",
  },
  {
    value: "enterprise" as const,
    label: "Enterprise (100,000+ items)",
    description: "Large enterprises with complex operations",
  },
];

export default function WarehouseSetupPage() {
  const router = useRouter();
  const {
    setupWarehouse,
    sessionData,
    isLoading: onboardingLoading,
  } = useOnboarding();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    setValue,
    watch,
  } = useForm<WarehouseSetupForm>({
    resolver: zodResolver(warehouseSetupSchema),
    mode: "onChange",
    defaultValues: {
      warehouseName: "",
      address: "",
      warehouseType: undefined,
      expectedVolume: undefined,
      description: "",
    },
  });

  const selectedType = watch("warehouseType");
  const selectedVolume = watch("expectedVolume");

  const onSubmit = async (data: WarehouseSetupForm) => {
    if (!sessionData?.sessionId) {
      toast.error("Session expired. Please start over.");
      router.push("/auth/signup/business");
      return;
    }

    setIsSubmitting(true);
    try {
      await setupWarehouse({
        warehouseName: data.warehouseName,
        address: data.address || undefined,
        warehouseType: data.warehouseType,
        expectedVolume: data.expectedVolume,
      });

      toast.success("Warehouse setup completed successfully!");
      router.push("/auth/signup/business/team");
    } catch (error) {
      console.error("Failed to setup warehouse:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Failed to setup warehouse";
      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleBack = () => {
    router.push("/auth/signup/business/account");
  };

  if (onboardingLoading) {
    return (
      <OnboardingLayout
        currentStep="warehouse_setup"
        title="Warehouse Setup"
        description="Configure your warehouse details"
        showBackButton={false}
      >
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-gray-600">Loading...</p>
          </div>
        </div>
      </OnboardingLayout>
    );
  }

  return (
    <OnboardingLayout
      currentStep="warehouse_setup"
      title="Warehouse Setup"
      description="Configure your warehouse to start managing inventory"
      onBack={handleBack}
    >
      <OnboardingFormWrapper
        title="Warehouse Configuration"
        description="Set up your warehouse details to optimize inventory management for your business."
        isSubmitting={isSubmitting}
        nextButtonText="Continue to Team Setup"
        nextButtonDisabled={!isValid}
        onSubmit={handleSubmit(onSubmit)}
        onBack={handleBack}
      >
        <div className="space-y-6">
          {/* Warehouse Name */}
          <OnboardingField
            label="Warehouse Name"
            required
            error={errors.warehouseName?.message}
            description="A descriptive name for your warehouse (e.g., 'Main Distribution Center')"
          >
            <Input
              {...register("warehouseName")}
              placeholder="Enter warehouse name"
              className="h-12"
              autoFocus
            />
          </OnboardingField>

          {/* Address */}
          <OnboardingField
            label="Address"
            error={errors.address?.message}
            description="Optional. Physical address of your warehouse for shipping and logistics."
          >
            <Textarea
              {...register("address")}
              placeholder="Enter warehouse address"
              className="min-h-[80px] resize-none"
              rows={3}
            />
          </OnboardingField>

          {/* Warehouse Type */}
          <OnboardingField
            label="Warehouse Type"
            required
            error={errors.warehouseType?.message}
            description="Select the type that best describes your warehouse operations."
          >
            <div className="grid grid-cols-1 gap-3">
              {WAREHOUSE_TYPES.map((type) => (
                <label
                  key={type.value}
                  className={`
                    relative flex cursor-pointer rounded-lg border p-4 transition-all
                    ${
                      selectedType === type.value
                        ? "border-primary bg-primary/5 ring-2 ring-primary/20"
                        : "border-gray-200 hover:border-gray-300"
                    }
                  `}
                >
                  <input
                    type="radio"
                    value={type.value}
                    {...register("warehouseType")}
                    className="sr-only"
                  />
                  <div className="flex-1">
                    <div className="flex items-center">
                      <div className="text-sm font-medium text-gray-900">
                        {type.label}
                      </div>
                    </div>
                    <div className="mt-1 text-sm text-gray-500">
                      {type.description}
                    </div>
                  </div>
                </label>
              ))}
            </div>
          </OnboardingField>

          {/* Expected Volume */}
          <OnboardingField
            label="Expected Volume"
            required
            error={errors.expectedVolume?.message}
            description="Estimate your inventory size to help us optimize the system for your needs."
          >
            <div className="grid grid-cols-1 gap-3">
              {VOLUME_OPTIONS.map((volume) => (
                <label
                  key={volume.value}
                  className={`
                    relative flex cursor-pointer rounded-lg border p-4 transition-all
                    ${
                      selectedVolume === volume.value
                        ? "border-primary bg-primary/5 ring-2 ring-primary/20"
                        : "border-gray-200 hover:border-gray-300"
                    }
                  `}
                >
                  <input
                    type="radio"
                    value={volume.value}
                    {...register("expectedVolume")}
                    className="sr-only"
                  />
                  <div className="flex-1">
                    <div className="flex items-center">
                      <div className="text-sm font-medium text-gray-900">
                        {volume.label}
                      </div>
                    </div>
                    <div className="mt-1 text-sm text-gray-500">
                      {volume.description}
                    </div>
                  </div>
                </label>
              ))}
            </div>
          </OnboardingField>

          {/* Description */}
          <OnboardingField
            label="Description"
            error={errors.description?.message}
            description="Optional. Additional details about your warehouse operations."
          >
            <Textarea
              {...register("description")}
              placeholder="Describe your warehouse operations, special requirements, etc."
              className="min-h-[100px] resize-none"
              rows={4}
            />
          </OnboardingField>
        </div>
      </OnboardingFormWrapper>
    </OnboardingLayout>
  );
}
