"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Progress } from "@/components/ui/progress";
import { ArrowLeft, ArrowRight, Building2, Package } from "lucide-react";
import { useOnboarding } from "@/components/providers/onboarding-provider";
import { OnboardingNavigationGuard } from "@/components/guards/OnboardingNavigationGuard";
import {
  useOnboardingValidation,
  businessInfoSchema,
} from "@/hooks/useOnboardingValidation";
import { BusinessInfo } from "@quildora/types";
import { toast } from "sonner";

const industryOptions = [
  "Manufacturing",
  "Distribution",
  "Retail",
  "Food & Beverage",
  "Automotive",
  "Electronics",
  "Pharmaceuticals",
  "Textiles",
  "Other",
];

const companySizeOptions = [
  "Small (1-50 employees)",
  "Medium (51-200 employees)",
  "Large (201-500 employees)",
  "Enterprise (500+ employees)",
];

const useCaseOptions = [
  "Inventory tracking and management",
  "Warehouse operations optimization",
  "Multi-location coordination",
  "Compliance and auditing",
  "Supply chain visibility",
  "Other",
];

function BusinessSignupPageContent() {
  const router = useRouter();
  const { updateBusinessInfo, isLoading, error } = useOnboarding();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<BusinessInfo>({
    resolver: zodResolver(businessInfoSchema),
    defaultValues: {
      companyName: "",
      industry: "",
      companySize: "",
      primaryUseCase: "",
    },
  });

  const onSubmit = async (data: BusinessInfo) => {
    setIsSubmitting(true);
    try {
      await updateBusinessInfo(data);
      toast.success("Business information saved successfully!");
      router.push("/auth/signup/business/account");
    } catch (error) {
      console.error("Failed to save business information:", error);
      toast.error("Failed to save business information. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleBack = () => {
    router.push("/auth/welcome");
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="w-full max-w-2xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <div className="bg-primary rounded-lg p-2 mr-3">
              <Package className="h-6 w-6 text-primary-foreground" />
            </div>
            <h1 className="text-2xl font-bold text-gray-900">Quildora</h1>
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Tell us about your business
          </h2>
          <p className="text-gray-600">
            Help us customize Quildora for your specific needs
          </p>
        </div>

        {/* Progress Indicator */}
        <div className="mb-8">
          <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
            <span>Step 1 of 4</span>
            <span>25% Complete</span>
          </div>
          <Progress value={25} className="h-2" />
          <div className="flex justify-between text-xs text-gray-500 mt-2">
            <span className="font-medium text-primary">Business Info</span>
            <span>Admin Account</span>
            <span>Warehouse Setup</span>
            <span>Complete</span>
          </div>
        </div>

        {/* Main Form */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Building2 className="mr-2 h-5 w-5" />
              Business Information
            </CardTitle>
            <CardDescription>
              This information helps us tailor the onboarding experience to your
              industry and use case.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-6"
              >
                {/* Company Name */}
                <FormField
                  control={form.control}
                  name="companyName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Company Name *</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter your company name"
                          {...field}
                          className="text-base h-12"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Industry */}
                <FormField
                  control={form.control}
                  name="industry"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Industry</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger className="text-base h-12">
                            <SelectValue placeholder="Select your industry" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {industryOptions.map((industry) => (
                            <SelectItem key={industry} value={industry}>
                              {industry}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Company Size */}
                <FormField
                  control={form.control}
                  name="companySize"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Company Size</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger className="text-base h-12">
                            <SelectValue placeholder="Select company size" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {companySizeOptions.map((size) => (
                            <SelectItem key={size} value={size}>
                              {size}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Primary Use Case */}
                <FormField
                  control={form.control}
                  name="primaryUseCase"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Primary Use Case</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger className="text-base h-12">
                            <SelectValue placeholder="What will you primarily use Quildora for?" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {useCaseOptions.map((useCase) => (
                            <SelectItem key={useCase} value={useCase}>
                              {useCase}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Error Display */}
                {error && (
                  <div className="bg-red-50 border border-red-200 rounded-md p-4">
                    <p className="text-red-800 text-sm">{error}</p>
                  </div>
                )}

                {/* Navigation Buttons */}
                <div className="flex justify-between pt-6">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleBack}
                    className="text-base px-6 py-3 h-auto"
                  >
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Back
                  </Button>

                  <Button
                    type="submit"
                    disabled={isSubmitting || isLoading}
                    className="text-base px-8 py-3 h-auto"
                  >
                    {isSubmitting || isLoading ? (
                      "Saving..."
                    ) : (
                      <>
                        Continue
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>

        {/* Help Text */}
        <div className="text-center mt-6">
          <p className="text-sm text-gray-500">
            Need help? Contact our support team at{" "}
            <a
              href="mailto:<EMAIL>"
              className="text-primary hover:underline"
            >
              <EMAIL>
            </a>
          </p>
        </div>
      </div>
    </div>
  );
}

export default function BusinessSignupPage() {
  return (
    <OnboardingNavigationGuard requiredStep="business_info">
      <BusinessSignupPageContent />
    </OnboardingNavigationGuard>
  );
}
