"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  CheckCircle2,
  Package,
  Users,
  Building2,
  ArrowRight,
  Sparkles,
  Copy,
  Mail,
} from "lucide-react";
import { useAuth } from "@/components/providers/auth-provider";
import { useOnboarding } from "@/components/providers/onboarding-provider";
import Confetti from "react-confetti";
import { toast } from "sonner";

export default function CompletePage() {
  const router = useRouter();
  const { appUser, appToken } = useAuth();
  const { sessionData } = useOnboarding();
  const [showConfetti, setShowConfetti] = useState(true);
  const [windowDimensions, setWindowDimensions] = useState({
    width: 0,
    height: 0,
  });
  const [invitations, setInvitations] = useState<any[]>([]);

  // Get window dimensions for confetti
  useEffect(() => {
    const updateWindowDimensions = () => {
      setWindowDimensions({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };

    updateWindowDimensions();
    window.addEventListener("resize", updateWindowDimensions);

    // Stop confetti after 5 seconds
    const timer = setTimeout(() => {
      setShowConfetti(false);
    }, 5000);

    return () => {
      window.removeEventListener("resize", updateWindowDimensions);
      clearTimeout(timer);
    };
  }, []);

  // Load invitations from localStorage
  useEffect(() => {
    const storedInvitations = localStorage.getItem("quildora_invitations");
    if (storedInvitations) {
      try {
        const parsedInvitations = JSON.parse(storedInvitations);
        setInvitations(parsedInvitations);
        // Clean up localStorage after loading
        localStorage.removeItem("quildora_invitations");
      } catch (error) {
        console.error("Failed to parse stored invitations:", error);
      }
    }
  }, []);

  // Redirect if not authenticated or no session data
  useEffect(() => {
    if (!appToken || !appUser) {
      router.push("/auth/welcome");
      return;
    }
  }, [appToken, appUser, router]);

  const handleGetStarted = () => {
    router.push("/");
  };

  const handleViewSettings = () => {
    router.push("/settings");
  };

  if (!appUser || !sessionData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 relative overflow-hidden">
      {/* Confetti */}
      {showConfetti && (
        <Confetti
          width={windowDimensions.width}
          height={windowDimensions.height}
          recycle={false}
          numberOfPieces={200}
          gravity={0.3}
        />
      )}

      <div className="flex items-center justify-center min-h-screen p-4">
        <div className="w-full max-w-2xl mx-auto">
          {/* Success Header */}
          <div className="text-center mb-8">
            <div className="relative inline-flex items-center justify-center w-20 h-20 bg-green-100 rounded-full mb-6">
              <CheckCircle2 className="w-10 h-10 text-green-600" />
              <div className="absolute -top-1 -right-1">
                <Sparkles className="w-6 h-6 text-yellow-500 animate-pulse" />
              </div>
            </div>

            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Welcome to Quildora! 🎉
            </h1>
            <p className="text-lg text-gray-600">
              Your warehouse management system is ready to go
            </p>
          </div>

          {/* Setup Summary */}
          <Card className="mb-8 shadow-lg">
            <CardContent className="p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-6">
                Setup Complete
              </h2>

              <div className="space-y-4">
                {/* Business Info */}
                <div className="flex items-center space-x-4 p-4 bg-green-50 rounded-lg">
                  <div className="flex-shrink-0 w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                    <Building2 className="w-5 h-5 text-green-600" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-medium text-gray-900">
                      Business Information
                    </h3>
                    <p className="text-sm text-gray-600">
                      {sessionData.businessInfo?.companyName || "Company"} is
                      ready for operations
                    </p>
                  </div>
                  <CheckCircle2 className="w-5 h-5 text-green-600" />
                </div>

                {/* Admin Account */}
                <div className="flex items-center space-x-4 p-4 bg-green-50 rounded-lg">
                  <div className="flex-shrink-0 w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                    <Users className="w-5 h-5 text-green-600" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-medium text-gray-900">
                      Administrator Account
                    </h3>
                    <p className="text-sm text-gray-600">
                      {appUser.name || appUser.email} has full system access
                    </p>
                  </div>
                  <CheckCircle2 className="w-5 h-5 text-green-600" />
                </div>

                {/* Warehouse */}
                <div className="flex items-center space-x-4 p-4 bg-green-50 rounded-lg">
                  <div className="flex-shrink-0 w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                    <Package className="w-5 h-5 text-green-600" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-medium text-gray-900">
                      Warehouse Setup
                    </h3>
                    <p className="text-sm text-gray-600">
                      {sessionData.warehouseSetup?.warehouseName ||
                        "Your warehouse"}{" "}
                      is configured and ready
                    </p>
                  </div>
                  <CheckCircle2 className="w-5 h-5 text-green-600" />
                </div>

                {/* Team Setup */}
                {sessionData.teamSetup?.inviteEmails?.length ? (
                  <div className="space-y-4">
                    <div className="flex items-center space-x-4 p-4 bg-green-50 rounded-lg">
                      <div className="flex-shrink-0 w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                        <Users className="w-5 h-5 text-green-600" />
                      </div>
                      <div className="flex-1">
                        <h3 className="font-medium text-gray-900">
                          Team Invitations Created
                        </h3>
                        <p className="text-sm text-gray-600">
                          {invitations.length > 0
                            ? invitations.length
                            : sessionData.teamSetup.inviteEmails.length}{" "}
                          team member
                          {(invitations.length > 0
                            ? invitations.length
                            : sessionData.teamSetup.inviteEmails.length) !== 1
                            ? "s"
                            : ""}{" "}
                          invited
                        </p>
                      </div>
                      <CheckCircle2 className="w-5 h-5 text-green-600" />
                    </div>

                    {invitations.length > 0 && (
                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <div className="flex items-center space-x-2 mb-3">
                          <Mail className="w-4 h-4 text-blue-600" />
                          <h4 className="font-medium text-blue-900">
                            Share Invitation Codes
                          </h4>
                        </div>
                        <p className="text-sm text-blue-700 mb-4">
                          Share these invitation codes with your team members so
                          they can join your workspace:
                        </p>
                        <div className="space-y-3">
                          {invitations.map((invitation, index) => (
                            <div
                              key={invitation.invitationId}
                              className="bg-white border border-blue-200 rounded-lg p-3"
                            >
                              <div className="flex items-center justify-between">
                                <div className="flex-1">
                                  <p className="text-sm font-medium text-gray-900">
                                    {sessionData.teamSetup?.inviteEmails?.[
                                      index
                                    ] || `Team Member ${index + 1}`}
                                  </p>
                                  <div className="flex items-center space-x-2 mt-1">
                                    <code className="text-sm font-mono bg-gray-100 px-2 py-1 rounded">
                                      {invitation.invitationCode}
                                    </code>
                                    <button
                                      onClick={() => {
                                        navigator.clipboard.writeText(
                                          invitation.invitationCode
                                        );
                                        toast.success(
                                          "Invitation code copied to clipboard!"
                                        );
                                      }}
                                      className="p-1 text-gray-500 hover:text-gray-700 transition-colors"
                                      title="Copy invitation code"
                                    >
                                      <Copy className="w-4 h-4" />
                                    </button>
                                  </div>
                                </div>
                              </div>
                              <p className="text-xs text-gray-500 mt-2">
                                Expires:{" "}
                                {new Date(
                                  invitation.expiresAt
                                ).toLocaleDateString()}
                              </p>
                            </div>
                          ))}
                        </div>
                        <div className="mt-4 p-3 bg-amber-50 border border-amber-200 rounded-lg">
                          <p className="text-sm text-amber-800">
                            <strong>How to share:</strong> Send these codes to
                            your team members and direct them to{" "}
                            <span className="font-mono bg-amber-100 px-1 rounded">
                              {window.location.origin}/auth/welcome
                            </span>{" "}
                            where they can click "Join Existing Team" and enter
                            their invitation code.
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="flex items-center space-x-4 p-4 bg-blue-50 rounded-lg">
                    <div className="flex-shrink-0 w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                      <Users className="w-5 h-5 text-blue-600" />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-medium text-gray-900">Team Setup</h3>
                      <p className="text-sm text-gray-600">
                        You can invite team members later from settings
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Next Steps */}
          <Card className="mb-8 shadow-lg">
            <CardContent className="p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                What's Next?
              </h2>

              <div className="space-y-3 text-sm text-gray-600">
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-primary rounded-full"></div>
                  <span>Start receiving inventory and creating pallets</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-primary rounded-full"></div>
                  <span>Set up your warehouse locations and zones</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-primary rounded-full"></div>
                  <span>Configure your items and inventory categories</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-primary rounded-full"></div>
                  <span>Invite additional team members as needed</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4">
            <Button
              onClick={handleGetStarted}
              className="flex-1 h-12 text-lg font-medium"
            >
              Get Started
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>

            <Button
              variant="outline"
              onClick={handleViewSettings}
              className="flex-1 h-12 text-lg font-medium"
            >
              View Settings
            </Button>
          </div>

          {/* Help Text */}
          <div className="text-center mt-8">
            <p className="text-sm text-gray-500">
              Need help getting started? Contact our support team at{" "}
              <a
                href="mailto:<EMAIL>"
                className="text-primary hover:underline"
              >
                <EMAIL>
              </a>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
