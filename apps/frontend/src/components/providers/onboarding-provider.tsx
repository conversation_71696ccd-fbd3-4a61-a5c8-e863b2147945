"use client";

import React, {
  createContext,
  useContext,
  useReducer,
  useCallback,
  useEffect,
} from "react";
import {
  OnboardingContextType,
  OnboardingState,
  OnboardingStep,
  BusinessInfo,
  AdminAccount,
  WarehouseSetup,
  TeamSetup,
} from "@quildora/types";
import { fetchWithAuth } from "@/lib/api";
import { useAuth } from "./auth-provider";
import { toast } from "sonner";
import {
  OnboardingErrorRecovery,
  OnboardingErrorType,
  OnboardingError,
} from "@/lib/onboarding-error-recovery";

// Onboarding reducer actions
type OnboardingAction =
  | { type: "SET_LOADING"; payload: boolean }
  | { type: "SET_ERROR"; payload: string | null }
  | { type: "SET_STEP"; payload: OnboardingStep }
  | { type: "SET_SESSION_DATA"; payload: Partial<OnboardingState> }
  | { type: "SET_SESSION_ID"; payload: string }
  | { type: "RESET_ONBOARDING" }
  | { type: "SAVE_PROGRESS" }
  | { type: "LOAD_PROGRESS"; payload: OnboardingState }
  | { type: "SET_RECOVERY_ERROR"; payload: OnboardingError }
  | { type: "CLEAR_RECOVERY" };

// Initial state
const initialState: OnboardingState & {
  isLoading: boolean;
  error: string | null;
  sessionId?: string;
  recoveryAvailable: boolean;
  lastError?: OnboardingError;
} = {
  step: "business_info",
  isLoading: false,
  error: null,
  recoveryAvailable: false,
};

// Reducer function
function onboardingReducer(
  state: OnboardingState & {
    isLoading: boolean;
    error: string | null;
    sessionId?: string;
    recoveryAvailable: boolean;
    lastError?: OnboardingError;
  },
  action: OnboardingAction
) {
  switch (action.type) {
    case "SET_LOADING":
      return { ...state, isLoading: action.payload };
    case "SET_ERROR":
      return { ...state, error: action.payload, isLoading: false };
    case "SET_STEP":
      return { ...state, step: action.payload };
    case "SET_SESSION_DATA":
      return { ...state, ...action.payload };
    case "SET_SESSION_ID":
      return { ...state, sessionId: action.payload };
    case "RESET_ONBOARDING":
      return { ...initialState };
    case "SAVE_PROGRESS":
      // Save to localStorage
      if (typeof window !== "undefined") {
        localStorage.setItem(
          "quildora_onboarding_progress",
          JSON.stringify(state)
        );
      }
      return state;
    case "LOAD_PROGRESS":
      return { ...state, ...action.payload };
    case "SET_RECOVERY_ERROR":
      return {
        ...state,
        lastError: action.payload,
        recoveryAvailable: action.payload.retryable,
        error: action.payload.message,
      };
    case "CLEAR_RECOVERY":
      return {
        ...state,
        lastError: undefined,
        recoveryAvailable: false,
        error: null,
      };
    default:
      return state;
  }
}

// Create context
const OnboardingContext = createContext<OnboardingContextType | undefined>(
  undefined
);

// Provider component
export function OnboardingProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const [state, dispatch] = useReducer(onboardingReducer, initialState);
  const { appToken } = useAuth();

  // Error recovery helper
  const handleErrorWithRecovery = useCallback(
    (error: Error, step: OnboardingStep, field?: string) => {
      const onboardingError: OnboardingError = {
        type:
          error.message.includes("network") || error.message.includes("fetch")
            ? OnboardingErrorType.NETWORK_ERROR
            : error.message.includes("session") ||
              error.message.includes("expired")
            ? OnboardingErrorType.SESSION_EXPIRED
            : error.message.includes("validation")
            ? OnboardingErrorType.VALIDATION_ERROR
            : OnboardingErrorType.SERVER_ERROR,
        message: error.message,
        step,
        field,
        retryable: !error.message.includes("validation"),
        timestamp: Date.now(),
      };

      dispatch({ type: "SET_RECOVERY_ERROR", payload: onboardingError });
      OnboardingErrorRecovery.logError(onboardingError);

      return onboardingError;
    },
    []
  );

  // Recovery actions
  const retryLastAction = useCallback(async () => {
    if (!state.lastError || !state.recoveryAvailable) return;

    const recoveryActions = OnboardingErrorRecovery.getRecoveryActions(
      state.lastError,
      {
        retryCallback: async () => {
          // Implement retry logic based on the error step
          console.log("Retrying last action for step:", state.lastError?.step);
        },
        restartCallback: () => {
          dispatch({ type: "RESET_ONBOARDING" });
        },
        reloadCallback: () => {
          window.location.reload();
        },
      }
    );
    const retryAction = recoveryActions.find(
      (action) => action.strategy === "RETRY"
    );

    if (retryAction) {
      dispatch({ type: "CLEAR_RECOVERY" });
      await retryAction.action();
    }
  }, [state.lastError, state.recoveryAvailable]);

  const clearError = useCallback(() => {
    dispatch({ type: "CLEAR_RECOVERY" });
  }, []);

  // Load progress from localStorage on mount
  useEffect(() => {
    if (typeof window !== "undefined") {
      const saved = localStorage.getItem("quildora_onboarding_progress");
      if (saved) {
        try {
          const savedState = JSON.parse(saved);
          dispatch({ type: "LOAD_PROGRESS", payload: savedState });
        } catch (error) {
          console.warn("Failed to load onboarding progress:", error);
        }
      }
    }
  }, []);

  // API call helper
  // For authenticated API calls
  const makeApiCall = useCallback(
    async (url: string, data: any) => {
      if (!appToken) {
        throw new Error("Authentication token not available");
      }

      const response = await fetchWithAuth(url, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
        token: appToken,
      });

      if (!response.ok) {
        const errorData = await response
          .json()
          .catch(() => ({ message: "Unknown error" }));
        throw new Error(errorData.message || "API request failed");
      }

      return response.json();
    },
    [appToken]
  );

  // For public API calls (no authentication required)
  const makePublicApiCall = useCallback(async (url: string, data: any) => {
    const baseUrl =
      process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:3001";
    const fullUrl = url.startsWith("/api")
      ? `${baseUrl}${url}`
      : `${baseUrl}/api${url}`;

    const response = await fetch(fullUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorData = await response
        .json()
        .catch(() => ({ message: "Unknown error" }));
      throw new Error(errorData.message || "API request failed");
    }

    return response.json();
  }, []);

  // Update business info
  const updateBusinessInfo = useCallback(
    async (info: BusinessInfo) => {
      dispatch({ type: "SET_LOADING", payload: true });
      dispatch({ type: "SET_ERROR", payload: null });

      try {
        const response = await makePublicApiCall(
          "/api/onboarding/business/start",
          {
            companyName: info.companyName,
            industry: info.industry,
            companySize: info.companySize,
            primaryUseCase: info.primaryUseCase,
          }
        );

        dispatch({ type: "SET_SESSION_ID", payload: response.sessionId });
        dispatch({ type: "SET_SESSION_DATA", payload: { businessInfo: info } });
        dispatch({ type: "SET_STEP", payload: response.nextStep });
        dispatch({ type: "SAVE_PROGRESS" });

        toast.success("Business information saved successfully");
      } catch (error) {
        const errorMessage =
          error instanceof Error
            ? error.message
            : "Failed to save business information";
        dispatch({ type: "SET_ERROR", payload: errorMessage });
        toast.error(errorMessage);
        throw error;
      } finally {
        dispatch({ type: "SET_LOADING", payload: false });
      }
    },
    [makePublicApiCall]
  );

  // Create admin account
  const createAdminAccount = useCallback(
    async (account: AdminAccount) => {
      if (!state.sessionId) {
        throw new Error("No active onboarding session");
      }

      dispatch({ type: "SET_LOADING", payload: true });
      dispatch({ type: "SET_ERROR", payload: null });

      try {
        const response = await makePublicApiCall(
          "/api/onboarding/business/admin-account",
          {
            sessionId: state.sessionId,
            fullName: account.fullName,
            email: account.email,
            password: account.password,
            phoneNumber: account.phoneNumber,
          }
        );

        dispatch({
          type: "SET_SESSION_DATA",
          payload: { adminAccount: account },
        });
        dispatch({ type: "SET_STEP", payload: response.nextStep });
        dispatch({ type: "SAVE_PROGRESS" });

        toast.success("Admin account created successfully");
      } catch (error) {
        const errorMessage =
          error instanceof Error
            ? error.message
            : "Failed to create admin account";
        dispatch({ type: "SET_ERROR", payload: errorMessage });
        toast.error(errorMessage);
        throw error;
      } finally {
        dispatch({ type: "SET_LOADING", payload: false });
      }
    },
    [makePublicApiCall, state.sessionId]
  );

  // Setup warehouse
  const setupWarehouse = useCallback(
    async (warehouse: WarehouseSetup) => {
      if (!state.sessionId) {
        throw new Error("No active onboarding session");
      }

      dispatch({ type: "SET_LOADING", payload: true });
      dispatch({ type: "SET_ERROR", payload: null });

      try {
        const response = await makePublicApiCall(
          "/api/onboarding/business/warehouse",
          {
            sessionId: state.sessionId,
            warehouseName: warehouse.warehouseName,
            address: warehouse.address,
            warehouseType: warehouse.warehouseType,
            expectedVolume: warehouse.expectedVolume,
          }
        );

        dispatch({
          type: "SET_SESSION_DATA",
          payload: { warehouseSetup: warehouse },
        });
        dispatch({ type: "SET_STEP", payload: response.nextStep });
        dispatch({ type: "SAVE_PROGRESS" });

        toast.success("Warehouse setup completed successfully");
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to setup warehouse";
        dispatch({ type: "SET_ERROR", payload: errorMessage });
        toast.error(errorMessage);
        throw error;
      } finally {
        dispatch({ type: "SET_LOADING", payload: false });
      }
    },
    [makePublicApiCall, state.sessionId]
  );

  // Complete onboarding
  const completeOnboarding = useCallback(
    async (team?: TeamSetup) => {
      if (!state.sessionId) {
        throw new Error("No active onboarding session");
      }

      dispatch({ type: "SET_LOADING", payload: true });
      dispatch({ type: "SET_ERROR", payload: null });

      try {
        const response = await makePublicApiCall(
          "/api/onboarding/business/complete",
          {
            sessionId: state.sessionId,
            teamSetup: team,
          }
        );

        if (team) {
          dispatch({ type: "SET_SESSION_DATA", payload: { teamSetup: team } });
        }
        dispatch({ type: "SET_STEP", payload: "completion" });

        // Clear localStorage on completion
        if (typeof window !== "undefined") {
          localStorage.removeItem("quildora_onboarding_progress");
        }

        toast.success(
          "Onboarding completed successfully! Welcome to Quildora!"
        );

        // Return the response for auth provider to handle login
        return response;
      } catch (error) {
        const errorMessage =
          error instanceof Error
            ? error.message
            : "Failed to complete onboarding";
        dispatch({ type: "SET_ERROR", payload: errorMessage });
        toast.error(errorMessage);
        throw error;
      } finally {
        dispatch({ type: "SET_LOADING", payload: false });
      }
    },
    [makePublicApiCall, state.sessionId]
  );

  // Navigation helpers
  const goToStep = useCallback((step: OnboardingStep) => {
    dispatch({ type: "SET_STEP", payload: step });
  }, []);

  const goToNextStep = useCallback(() => {
    const steps: OnboardingStep[] = [
      "business_info",
      "admin_account",
      "warehouse_setup",
      "team_setup",
      "completion",
    ];
    const currentIndex = steps.indexOf(state.step);
    if (currentIndex < steps.length - 1) {
      dispatch({ type: "SET_STEP", payload: steps[currentIndex + 1] });
    }
  }, [state.step]);

  const goToPreviousStep = useCallback(() => {
    const steps: OnboardingStep[] = [
      "business_info",
      "admin_account",
      "warehouse_setup",
      "team_setup",
      "completion",
    ];
    const currentIndex = steps.indexOf(state.step);
    if (currentIndex > 0) {
      dispatch({ type: "SET_STEP", payload: steps[currentIndex - 1] });
    }
  }, [state.step]);

  // State management helpers
  const resetOnboarding = useCallback(() => {
    dispatch({ type: "RESET_ONBOARDING" });
    if (typeof window !== "undefined") {
      localStorage.removeItem("quildora_onboarding_progress");
    }
  }, []);

  const saveProgress = useCallback(() => {
    dispatch({ type: "SAVE_PROGRESS" });
  }, []);

  const loadProgress = useCallback(() => {
    if (typeof window !== "undefined") {
      const saved = localStorage.getItem("quildora_onboarding_progress");
      if (saved) {
        try {
          const savedState = JSON.parse(saved);
          dispatch({ type: "LOAD_PROGRESS", payload: savedState });
        } catch (error) {
          console.warn("Failed to load onboarding progress:", error);
        }
      }
    }
  }, []);

  const contextValue: OnboardingContextType = {
    currentStep: state.step,
    sessionData: state,
    isLoading: state.isLoading,
    error: state.error,
    recoveryAvailable: state.recoveryAvailable,
    lastError: state.lastError,
    updateBusinessInfo,
    createAdminAccount,
    setupWarehouse,
    completeOnboarding,
    goToStep,
    goToNextStep,
    goToPreviousStep,
    resetOnboarding,
    saveProgress,
    loadProgress,
    retryLastAction,
    clearError,
  };

  return (
    <OnboardingContext.Provider value={contextValue}>
      {children}
    </OnboardingContext.Provider>
  );
}

// Hook to use onboarding context
export function useOnboarding() {
  const context = useContext(OnboardingContext);
  if (context === undefined) {
    throw new Error("useOnboarding must be used within an OnboardingProvider");
  }
  return context;
}
