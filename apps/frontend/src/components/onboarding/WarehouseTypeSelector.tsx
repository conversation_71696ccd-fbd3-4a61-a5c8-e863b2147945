'use client';

import React from 'react';
import { Building2, Factory, Store, Package, HelpCircle } from 'lucide-react';

interface WarehouseType {
  value: string;
  label: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  features: string[];
}

interface WarehouseTypeSelectorProps {
  value?: string;
  onChange: (value: string) => void;
  error?: string;
  disabled?: boolean;
  className?: string;
}

const WAREHOUSE_TYPES: WarehouseType[] = [
  {
    value: 'distribution',
    label: 'Distribution Center',
    description: 'Receiving, storing, and shipping products to customers or retailers',
    icon: Building2,
    features: [
      'High-volume receiving and shipping',
      'Cross-docking capabilities',
      'Multi-customer inventory',
      'Advanced picking systems',
    ],
  },
  {
    value: 'manufacturing',
    label: 'Manufacturing Facility',
    description: 'Production facility with raw materials and finished goods storage',
    icon: Factory,
    features: [
      'Raw materials management',
      'Work-in-progress tracking',
      'Finished goods storage',
      'Production scheduling integration',
    ],
  },
  {
    value: 'retail',
    label: 'Retail Store',
    description: 'Store with inventory for direct customer sales',
    icon: Store,
    features: [
      'Point-of-sale integration',
      'Customer-facing inventory',
      'Seasonal merchandise tracking',
      'Replenishment automation',
    ],
  },
  {
    value: 'storage',
    label: 'Storage Facility',
    description: 'Long-term storage of goods and materials',
    icon: Package,
    features: [
      'Long-term storage optimization',
      'Climate-controlled zones',
      'Inventory preservation',
      'Minimal handling operations',
    ],
  },
  {
    value: 'other',
    label: 'Other',
    description: 'Custom warehouse type not listed above',
    icon: HelpCircle,
    features: [
      'Flexible configuration',
      'Custom workflows',
      'Adaptable to unique needs',
      'Scalable operations',
    ],
  },
];

export default function WarehouseTypeSelector({
  value,
  onChange,
  error,
  disabled = false,
  className = '',
}: WarehouseTypeSelectorProps) {
  return (
    <div className={`space-y-4 ${className}`}>
      <div className="grid grid-cols-1 gap-4">
        {WAREHOUSE_TYPES.map((type) => {
          const Icon = type.icon;
          const isSelected = value === type.value;
          
          return (
            <label
              key={type.value}
              className={`
                relative flex cursor-pointer rounded-lg border p-4 transition-all
                ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
                ${isSelected
                  ? 'border-primary bg-primary/5 ring-2 ring-primary/20'
                  : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                }
                ${error ? 'border-red-300' : ''}
              `}
            >
              <input
                type="radio"
                value={type.value}
                checked={isSelected}
                onChange={(e) => onChange(e.target.value)}
                disabled={disabled}
                className="sr-only"
              />
              
              <div className="flex items-start space-x-4 flex-1">
                {/* Icon */}
                <div className={`
                  flex-shrink-0 w-10 h-10 rounded-lg flex items-center justify-center
                  ${isSelected 
                    ? 'bg-primary text-primary-foreground' 
                    : 'bg-gray-100 text-gray-600'
                  }
                `}>
                  <Icon className="w-5 h-5" />
                </div>
                
                {/* Content */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <h3 className="text-sm font-medium text-gray-900">
                      {type.label}
                    </h3>
                    {isSelected && (
                      <div className="w-4 h-4 bg-primary rounded-full flex items-center justify-center">
                        <div className="w-2 h-2 bg-white rounded-full" />
                      </div>
                    )}
                  </div>
                  
                  <p className="mt-1 text-sm text-gray-500">
                    {type.description}
                  </p>
                  
                  {/* Features (shown when selected) */}
                  {isSelected && (
                    <div className="mt-3 space-y-1">
                      <p className="text-xs font-medium text-gray-700">Key Features:</p>
                      <ul className="text-xs text-gray-600 space-y-1">
                        {type.features.map((feature, index) => (
                          <li key={index} className="flex items-center">
                            <div className="w-1 h-1 bg-primary rounded-full mr-2 flex-shrink-0" />
                            {feature}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              </div>
            </label>
          );
        })}
      </div>
      
      {error && (
        <p className="text-sm text-red-600 flex items-center gap-1">
          <HelpCircle className="h-3 w-3" />
          {error}
        </p>
      )}
    </div>
  );
}

// Volume selector component
interface VolumeOption {
  value: string;
  label: string;
  description: string;
  range: string;
  recommended: string[];
}

interface VolumeSelectorProps {
  value?: string;
  onChange: (value: string) => void;
  error?: string;
  disabled?: boolean;
  className?: string;
}

const VOLUME_OPTIONS: VolumeOption[] = [
  {
    value: 'small',
    label: 'Small',
    description: 'Perfect for small businesses and startups',
    range: '1-1,000 items',
    recommended: ['Retail stores', 'Small manufacturers', 'E-commerce startups'],
  },
  {
    value: 'medium',
    label: 'Medium',
    description: 'Growing businesses with moderate inventory',
    range: '1,000-10,000 items',
    recommended: ['Regional distributors', 'Growing manufacturers', 'Multi-location retailers'],
  },
  {
    value: 'large',
    label: 'Large',
    description: 'Established businesses with high volume',
    range: '10,000-100,000 items',
    recommended: ['Large distributors', 'Major manufacturers', 'Enterprise retailers'],
  },
  {
    value: 'enterprise',
    label: 'Enterprise',
    description: 'Large enterprises with complex operations',
    range: '100,000+ items',
    recommended: ['Fortune 500 companies', 'Global distributors', 'Major supply chains'],
  },
];

export function VolumeSelector({
  value,
  onChange,
  error,
  disabled = false,
  className = '',
}: VolumeSelectorProps) {
  return (
    <div className={`space-y-4 ${className}`}>
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        {VOLUME_OPTIONS.map((option) => {
          const isSelected = value === option.value;
          
          return (
            <label
              key={option.value}
              className={`
                relative flex cursor-pointer rounded-lg border p-4 transition-all
                ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
                ${isSelected
                  ? 'border-primary bg-primary/5 ring-2 ring-primary/20'
                  : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                }
                ${error ? 'border-red-300' : ''}
              `}
            >
              <input
                type="radio"
                value={option.value}
                checked={isSelected}
                onChange={(e) => onChange(e.target.value)}
                disabled={disabled}
                className="sr-only"
              />
              
              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium text-gray-900">
                    {option.label} ({option.range})
                  </h3>
                  {isSelected && (
                    <div className="w-4 h-4 bg-primary rounded-full flex items-center justify-center">
                      <div className="w-2 h-2 bg-white rounded-full" />
                    </div>
                  )}
                </div>
                
                <p className="mt-1 text-sm text-gray-500">
                  {option.description}
                </p>
                
                {/* Recommended for (shown when selected) */}
                {isSelected && (
                  <div className="mt-3">
                    <p className="text-xs font-medium text-gray-700">Recommended for:</p>
                    <ul className="text-xs text-gray-600 mt-1 space-y-1">
                      {option.recommended.map((rec, index) => (
                        <li key={index} className="flex items-center">
                          <div className="w-1 h-1 bg-primary rounded-full mr-2 flex-shrink-0" />
                          {rec}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </label>
          );
        })}
      </div>
      
      {error && (
        <p className="text-sm text-red-600 flex items-center gap-1">
          <HelpCircle className="h-3 w-3" />
          {error}
        </p>
      )}
    </div>
  );
}

// Export types
export type { WarehouseType, VolumeOption, WarehouseTypeSelectorProps, VolumeSelectorProps };
