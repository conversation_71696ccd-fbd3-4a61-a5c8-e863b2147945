'use client';

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { motion } from 'framer-motion';
import { AlertTriangle, RefreshCw, Home, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  retryCount: number;
}

export class OnboardingErrorBoundary extends Component<Props, State> {
  private maxRetries = 3;

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      error,
      errorInfo,
    });

    // Log error to monitoring service
    console.error('Onboarding Error Boundary caught an error:', error, errorInfo);
    
    // Call custom error handler if provided
    this.props.onError?.(error, errorInfo);

    // Report to error tracking service (e.g., Sentry)
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'exception', {
        description: error.toString(),
        fatal: false,
      });
    }
  }

  handleRetry = () => {
    if (this.state.retryCount < this.maxRetries) {
      this.setState(prevState => ({
        hasError: false,
        error: null,
        errorInfo: null,
        retryCount: prevState.retryCount + 1,
      }));
    }
  };

  handleReset = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0,
    });
  };

  handleGoHome = () => {
    window.location.href = '/';
  };

  handleGoBack = () => {
    window.history.back();
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-50 flex items-center justify-center p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3 }}
            className="w-full max-w-md mx-auto"
          >
            <Card className="shadow-lg border-red-200">
              <CardContent className="p-6 text-center">
                {/* Error Icon */}
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.2, type: 'spring' }}
                  className="mx-auto mb-4"
                >
                  <div className="bg-red-100 rounded-full p-3 inline-flex">
                    <AlertTriangle className="h-8 w-8 text-red-600" />
                  </div>
                </motion.div>

                {/* Error Message */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                  className="mb-6"
                >
                  <h2 className="text-xl font-semibold text-gray-900 mb-2">
                    Oops! Something went wrong
                  </h2>
                  <p className="text-gray-600 text-sm mb-4">
                    We encountered an unexpected error during your onboarding process. 
                    Don't worry - your progress has been saved.
                  </p>
                  
                  {/* Error Details (only in development) */}
                  {process.env.NODE_ENV === 'development' && this.state.error && (
                    <details className="text-left bg-gray-50 rounded-lg p-3 mb-4">
                      <summary className="cursor-pointer text-sm font-medium text-gray-700 mb-2">
                        Error Details (Development)
                      </summary>
                      <pre className="text-xs text-gray-600 overflow-auto">
                        {this.state.error.toString()}
                        {this.state.errorInfo?.componentStack}
                      </pre>
                    </details>
                  )}
                </motion.div>

                {/* Action Buttons */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4 }}
                  className="space-y-3"
                >
                  {/* Retry Button */}
                  {this.state.retryCount < this.maxRetries && (
                    <Button
                      onClick={this.handleRetry}
                      className="w-full flex items-center justify-center"
                      variant="default"
                    >
                      <RefreshCw className="h-4 w-4 mr-2" />
                      Try Again ({this.maxRetries - this.state.retryCount} attempts left)
                    </Button>
                  )}

                  {/* Alternative Actions */}
                  <div className="flex gap-2">
                    <Button
                      onClick={this.handleGoBack}
                      variant="outline"
                      className="flex-1 flex items-center justify-center"
                    >
                      <ArrowLeft className="h-4 w-4 mr-2" />
                      Go Back
                    </Button>
                    
                    <Button
                      onClick={this.handleGoHome}
                      variant="outline"
                      className="flex-1 flex items-center justify-center"
                    >
                      <Home className="h-4 w-4 mr-2" />
                      Home
                    </Button>
                  </div>

                  {/* Reset Button (if max retries reached) */}
                  {this.state.retryCount >= this.maxRetries && (
                    <Button
                      onClick={this.handleReset}
                      variant="secondary"
                      className="w-full"
                    >
                      Start Fresh
                    </Button>
                  )}
                </motion.div>

                {/* Help Text */}
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.6 }}
                  className="mt-6 pt-4 border-t border-gray-200"
                >
                  <p className="text-xs text-gray-500">
                    If this problem persists, please contact our support team at{' '}
                    <a
                      href="mailto:<EMAIL>"
                      className="text-primary hover:underline"
                    >
                      <EMAIL>
                    </a>
                  </p>
                </motion.div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      );
    }

    return this.props.children;
  }
}

/**
 * Hook for handling async errors in functional components
 */
export function useErrorHandler() {
  const [error, setError] = React.useState<Error | null>(null);

  const resetError = React.useCallback(() => {
    setError(null);
  }, []);

  const handleError = React.useCallback((error: Error) => {
    console.error('Async error caught:', error);
    setError(error);
  }, []);

  // Throw error to be caught by error boundary
  React.useEffect(() => {
    if (error) {
      throw error;
    }
  }, [error]);

  return { handleError, resetError };
}

/**
 * Error fallback component for specific error types
 */
export function NetworkErrorFallback({ 
  onRetry, 
  onGoBack 
}: { 
  onRetry: () => void; 
  onGoBack: () => void; 
}) {
  return (
    <div className="text-center p-6">
      <div className="bg-yellow-100 rounded-full p-3 inline-flex mb-4">
        <AlertTriangle className="h-6 w-6 text-yellow-600" />
      </div>
      <h3 className="text-lg font-semibold text-gray-900 mb-2">
        Connection Problem
      </h3>
      <p className="text-gray-600 text-sm mb-4">
        We're having trouble connecting to our servers. Please check your internet connection and try again.
      </p>
      <div className="flex gap-2 justify-center">
        <Button onClick={onRetry} size="sm">
          <RefreshCw className="h-4 w-4 mr-2" />
          Retry
        </Button>
        <Button onClick={onGoBack} variant="outline" size="sm">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Go Back
        </Button>
      </div>
    </div>
  );
}

/**
 * Validation error fallback component
 */
export function ValidationErrorFallback({ 
  errors, 
  onFix 
}: { 
  errors: string[]; 
  onFix: () => void; 
}) {
  return (
    <div className="text-center p-6">
      <div className="bg-red-100 rounded-full p-3 inline-flex mb-4">
        <AlertTriangle className="h-6 w-6 text-red-600" />
      </div>
      <h3 className="text-lg font-semibold text-gray-900 mb-2">
        Please Fix These Issues
      </h3>
      <div className="text-left bg-red-50 rounded-lg p-3 mb-4">
        <ul className="text-sm text-red-700 space-y-1">
          {errors.map((error, index) => (
            <li key={index} className="flex items-start">
              <span className="text-red-500 mr-2">•</span>
              {error}
            </li>
          ))}
        </ul>
      </div>
      <Button onClick={onFix} size="sm">
        Fix Issues
      </Button>
    </div>
  );
}
