import React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardT<PERSON><PERSON> } from "@/components/ui/card";
import { List } from "lucide-react";
import DestinationFilter from "./DestinationFilter";

interface DestinationSelectionCardProps {
  destinations: string[];
  selectedDestination: string;
  onDestinationSelect: (destination: string) => void;
}

/**
 * Reusable component for destination selection in picking workflow
 * Encapsulates destination filtering functionality in a card layout
 */
export const DestinationSelectionCard: React.FC<DestinationSelectionCardProps> = ({
  destinations,
  selectedDestination,
  onDestinationSelect,
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <List className="h-5 w-5" />
          Select Destination
        </CardTitle>
      </CardHeader>
      <CardContent>
        <DestinationFilter
          destinations={destinations}
          selectedDestination={selectedDestination}
          onDestinationSelect={onDestinationSelect}
        />
      </CardContent>
    </Card>
  );
};
