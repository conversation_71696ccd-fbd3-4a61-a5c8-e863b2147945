"use client";

import { useState, useMemo, useCallback, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Search, X, Loader2, CheckCircle2 } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { useDestinationsWithCodesSuspense } from "@/hooks/api/useWarehouseData";
import { cn } from "@/lib/utils";

interface DestinationFilterProps {
  destinations: string[];
  selectedDestination: string;
  onDestinationSelect: (destination: string) => void;
  isProcessing?: boolean;
  onProcessingStart?: () => void;
}

export default function DestinationFilter({
  destinations,
  selectedDestination,
  onDestinationSelect,
  isProcessing = false,
  onProcessingStart,
}: DestinationFilterProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [optimisticSelection, setOptimisticSelection] = useState<string>("");
  const [processingDestination, setProcessingDestination] =
    useState<string>("");

  // Fetch destinations with codes for enhanced search and caching
  const { data: destinationsWithCodes = [] } =
    useDestinationsWithCodesSuspense();

  // Reset optimistic state when processing completes
  useEffect(() => {
    if (!isProcessing && selectedDestination) {
      // Clear optimistic state after processing completes
      setOptimisticSelection("");
      setProcessingDestination("");
    }
  }, [isProcessing, selectedDestination]);

  const destinationCodeMap = useMemo(
    () => new Map(destinationsWithCodes.map((dest) => [dest.name, dest.code])),
    [destinationsWithCodes]
  );

  // Filter destinations based on search term (name or code)
  const filteredDestinations = useMemo(() => {
    if (!searchTerm.trim()) return destinations;

    return destinations.filter((destination) => {
      const lowerSearchTerm = searchTerm.toLowerCase();
      const destinationCode = destinationCodeMap.get(destination);

      // Search by destination name or code
      return (
        destination.toLowerCase().includes(lowerSearchTerm) ||
        (destinationCode &&
          destinationCode.toLowerCase().includes(lowerSearchTerm))
      );
    });
  }, [destinations, searchTerm, destinationCodeMap]);

  // Optimistic click handler with immediate feedback
  const handleDestinationClick = useCallback(
    async (destination: string) => {
      // Immediate visual feedback - optimistic update
      setOptimisticSelection(destination);
      setProcessingDestination(destination);

      // Notify parent that processing is starting
      onProcessingStart?.();

      // Small delay to ensure UI updates are visible and provide satisfying feedback
      await new Promise((resolve) => setTimeout(resolve, 100));

      // Call the actual handler
      onDestinationSelect(destination);
    },
    [onDestinationSelect, onProcessingStart]
  );

  const handleClearSelection = useCallback(() => {
    setOptimisticSelection("");
    setProcessingDestination("");
    onDestinationSelect("");
    setSearchTerm("");
  }, [onDestinationSelect]);

  // Helper function to format destination display with code
  const formatDestinationDisplay = useCallback(
    (destination: string) => {
      const destinationCode = destinationCodeMap.get(destination);
      if (destinationCode) {
        return `${destination} (${destinationCode})`;
      }
      return destination;
    },
    [destinationCodeMap]
  );

  const destinationButtons = useMemo(
    () =>
      filteredDestinations.map((destination) => {
        const isSelected =
          selectedDestination === destination ||
          optimisticSelection === destination;
        const isCurrentlyProcessing = processingDestination === destination;
        const isDisabled = isProcessing || isCurrentlyProcessing;

        return (
          <Button
            key={destination}
            variant={isSelected ? "default" : "outline"}
            className={cn(
              "w-full justify-between text-xl h-12 transition-all duration-200",
              "hover:scale-[0.98] active:scale-[0.96] hover:shadow-md", // Touch feedback with shadow
              "focus:ring-2 focus:ring-offset-2 focus:ring-blue-500", // Accessibility
              isCurrentlyProcessing &&
                "animate-pulse bg-blue-50 border-blue-300",
              isDisabled && "opacity-60 cursor-not-allowed",
              isSelected &&
                !isCurrentlyProcessing &&
                "shadow-lg transform scale-[1.02]" // Selected state enhancement
            )}
            onClick={() => handleDestinationClick(destination)}
            disabled={isDisabled}
          >
            <span>{formatDestinationDisplay(destination)}</span>
            {isCurrentlyProcessing && (
              <Loader2 className="h-4 w-4 animate-spin ml-2" />
            )}
            {isSelected && !isCurrentlyProcessing && (
              <CheckCircle2 className="h-4 w-4 ml-2 text-green-500" />
            )}
          </Button>
        );
      }),
    [
      filteredDestinations,
      selectedDestination,
      optimisticSelection,
      processingDestination,
      isProcessing,
      handleDestinationClick,
      formatDestinationDisplay,
    ]
  );

  const selectedDestinationDisplay = useMemo(() => {
    const displayDestination = selectedDestination || optimisticSelection;
    return displayDestination
      ? formatDestinationDisplay(displayDestination)
      : "";
  }, [selectedDestination, optimisticSelection, formatDestinationDisplay]);

  return (
    <div className="space-y-4">
      {/* Search Input */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search destinations by name or code..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10 text-xl h-12"
        />
      </div>

      {/* Selected Destination */}
      {(selectedDestination || optimisticSelection) && (
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">
            {isProcessing ? "Processing:" : "Selected:"}
          </span>
          <Badge
            variant="default"
            className={cn(
              "flex items-center gap-2",
              isProcessing && "animate-pulse"
            )}
          >
            {selectedDestinationDisplay}
            {isProcessing && <Loader2 className="h-3 w-3 animate-spin" />}
            {!isProcessing && (
              <Button
                variant="ghost"
                size="sm"
                className="h-4 w-4 p-0 hover:bg-transparent"
                onClick={handleClearSelection}
              >
                <X className="h-3 w-3" />
              </Button>
            )}
          </Badge>
        </div>
      )}

      {/* Destination List */}
      <div className="space-y-2 max-h-60 overflow-y-auto">
        {isProcessing && filteredDestinations.length > 0 ? (
          // Show skeleton loading when processing
          <div className="space-y-2">
            {Array.from({
              length: Math.min(3, filteredDestinations.length),
            }).map((_, i) => (
              <Skeleton key={i} className="h-12 w-full" />
            ))}
            <div className="text-center py-2 text-sm text-muted-foreground">
              Loading destination data...
            </div>
          </div>
        ) : filteredDestinations.length === 0 ? (
          <div className="text-center py-4 text-muted-foreground">
            {searchTerm
              ? "No destinations match your search"
              : "No destinations available"}
          </div>
        ) : (
          destinationButtons
        )}
      </div>
    </div>
  );
}
