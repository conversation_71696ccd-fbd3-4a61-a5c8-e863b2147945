"use client";

import { useState, useMemo, useCallback } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Search, X } from "lucide-react";
import { useDestinationsWithCodesSuspense } from "@/hooks/api/useWarehouseData";

interface DestinationFilterProps {
  destinations: string[];
  selectedDestination: string;
  onDestinationSelect: (destination: string) => void;
}

export default function DestinationFilter({
  destinations,
  selectedDestination,
  onDestinationSelect,
}: DestinationFilterProps) {
  const [searchTerm, setSearchTerm] = useState("");

  // Fetch destinations with codes for enhanced search and cache
  const { data: destinationsWithCodes = [] } = useDestinationsWithCodesSuspense();

  const destinationCodeMap = useMemo(() => new Map(
      destinationsWithCodes.map((dest) => [dest.name, dest.code])
    ), [destinationsWithCodes]);

  // Filter destinations based on search term (name or code)
  const filteredDestinations = useMemo(() => {
    if (!searchTerm.trim()) return destinations;

    return destinations.filter((destination) => {
      const lowerSearchTerm = searchTerm.toLowerCase();
      const destinationCode = destinationCodeMap.get(destination);

      // Search by destination name or code
      return (
        destination.toLowerCase().includes(lowerSearchTerm) ||
        (destinationCode &&
          destinationCode.toLowerCase().includes(lowerSearchTerm))
      );
    });
  }, [destinations, searchTerm, destinationCodeMap]);

  const handleClearSelection = () => {
    onDestinationSelect("");
    setSearchTerm("");
  };

  // Helper function to format destination display with code
  const formatDestinationDisplay = useCallback((destination: string) => {
    const destinationCode = destinationCodeMap.get(destination);
    if (destinationCode) {
      return `${destination} (${destinationCode})`;
    }
    return destination;
  }, [destinationCodeMap]);

  const destinationButtons = useMemo(() => 
  filteredDestinations.map((destination) => (
    <Button
      key={destination}
      variant={selectedDestination === destination ? "default" : "outline"}
      className="w-full justify-start text-xl h-12"
      onClick={() => onDestinationSelect(destination)}
    >
      {formatDestinationDisplay(destination)}
    </Button>
  ))
, [filteredDestinations, selectedDestination, onDestinationSelect, formatDestinationDisplay]);

  const selectedDestinationDisplay = useMemo(() => 
  selectedDestination ? formatDestinationDisplay(selectedDestination) : ""
  , [selectedDestination, formatDestinationDisplay]);

  return (
    <div className="space-y-4">
      {/* Search Input */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search destinations by name or code..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10 text-xl h-12"
        />
      </div>

      {/* Selected Destination */}
      {selectedDestination && (
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">Selected:</span>
          <Badge variant="default" className="flex items-center gap-1">
            {selectedDestinationDisplay}
            <Button
              variant="ghost"
              size="sm"
              className="h-4 w-4 p-0 hover:bg-transparent"
              onClick={handleClearSelection}
            >
              <X className="h-3 w-3" />
            </Button>
          </Badge>
        </div>
      )}

      {/* Destination List */}
      <div className="space-y-2 max-h-60 overflow-y-auto">
        {filteredDestinations.length === 0 ? (
          <div className="text-center py-4 text-muted-foreground">
            {searchTerm
              ? "No destinations match your search"
              : "No destinations available"}
          </div>
        ) : (destinationButtons)}
      </div>
    </div>
  );
}
