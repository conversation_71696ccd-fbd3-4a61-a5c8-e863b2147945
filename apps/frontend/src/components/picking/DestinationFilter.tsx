"use client";

import { useState, useMemo } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Search, X } from "lucide-react";
import { useDestinationsWithCodes } from "@/hooks/api/useWarehouseData";
import { useQuery } from "@tanstack/react-query";

interface DestinationFilterProps {
  destinations: string[];
  selectedDestination: string;
  onDestinationSelect: (destination: string) => void;
}

export default function DestinationFilter({
  destinations,
  selectedDestination,
  onDestinationSelect,
}: DestinationFilterProps) {
  const [searchTerm, setSearchTerm] = useState("");

  // Fetch destinations with codes for enhanced search
  const { data: destinationsWithCodes = [] } = useDestinationsWithCodes();

  // Filter destinations based on search term (name or code)
  const filteredDestinations = useMemo(() => {
    if (!searchTerm.trim()) return destinations;

    // Create a map of destination names to codes for quick lookup
    const destinationCodeMap = new Map(
      destinationsWithCodes.map((dest) => [dest.name, dest.code])
    );

    return destinations.filter((destination) => {
      const lowerSearchTerm = searchTerm.toLowerCase();
      const destinationCode = destinationCodeMap.get(destination);

      // Search by destination name or code
      return (
        destination.toLowerCase().includes(lowerSearchTerm) ||
        (destinationCode &&
          destinationCode.toLowerCase().includes(lowerSearchTerm))
      );
    });
  }, [destinations, searchTerm, destinationsWithCodes]);

  const handleClearSelection = () => {
    onDestinationSelect("");
    setSearchTerm("");
  };

  // Helper function to format destination display with code
  const formatDestinationDisplay = (destination: string) => {
    const destinationWithCode = destinationsWithCodes.find(
      (dest) => dest.name === destination
    );
    if (destinationWithCode?.code) {
      return `${destination} (${destinationWithCode.code})`;
    }
    return destination;
  };

  return (
    <div className="space-y-4">
      {/* Search Input */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search destinations by name or code..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10 text-xl h-12"
        />
      </div>

      {/* Selected Destination */}
      {selectedDestination && (
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">Selected:</span>
          <Badge variant="default" className="flex items-center gap-1">
            {formatDestinationDisplay(selectedDestination)}
            <Button
              variant="ghost"
              size="sm"
              className="h-4 w-4 p-0 hover:bg-transparent"
              onClick={handleClearSelection}
            >
              <X className="h-3 w-3" />
            </Button>
          </Badge>
        </div>
      )}

      {/* Destination List */}
      <div className="space-y-2 max-h-60 overflow-y-auto">
        {filteredDestinations.length === 0 ? (
          <div className="text-center py-4 text-muted-foreground">
            {searchTerm
              ? "No destinations match your search"
              : "No destinations available"}
          </div>
        ) : (
          filteredDestinations.map((destination) => (
            <Button
              key={destination}
              variant={
                selectedDestination === destination ? "default" : "outline"
              }
              className="w-full justify-start text-xl h-12"
              onClick={() => onDestinationSelect(destination)}
            >
              {formatDestinationDisplay(destination)}
            </Button>
          ))
        )}
      </div>
    </div>
  );
}
