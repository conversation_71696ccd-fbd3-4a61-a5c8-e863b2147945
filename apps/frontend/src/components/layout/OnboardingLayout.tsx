"use client";

import React from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";

import { ArrowLeft, Package } from "lucide-react";
import { OnboardingStep } from "@quildora/types";
import {
  OnboardingProgressIndicator,
  OnboardingProgressMobile,
} from "@/components/onboarding/OnboardingProgressIndicator";

interface OnboardingLayoutProps {
  children: React.ReactNode;
  currentStep: OnboardingStep;
  title: string;
  description: string;
  showBackButton?: boolean;
  onBack?: () => void;
  backUrl?: string;
  maxWidth?: "sm" | "md" | "lg" | "xl";
}

const STEP_CONFIG = {
  business_info: { label: "Business Info", order: 1, total: 4 },
  admin_account: { label: "Admin Account", order: 2, total: 4 },
  warehouse_setup: { label: "Warehouse Setup", order: 3, total: 4 },
  team_setup: { label: "Team Setup", order: 4, total: 4 },
  completion: { label: "Complete", order: 4, total: 4 },
} as const;

const MAX_WIDTH_CLASSES = {
  sm: "max-w-md",
  md: "max-w-2xl",
  lg: "max-w-4xl",
  xl: "max-w-6xl",
} as const;

export default function OnboardingLayout({
  children,
  currentStep,
  title,
  description,
  showBackButton = true,
  onBack,
  backUrl,
  maxWidth = "md",
}: OnboardingLayoutProps) {
  const router = useRouter();

  const stepConfig = STEP_CONFIG[currentStep];
  const progressPercentage = stepConfig
    ? (stepConfig.order / stepConfig.total) * 100
    : 0;

  const handleBack = () => {
    if (onBack) {
      onBack();
    } else if (backUrl) {
      router.push(backUrl);
    } else {
      router.back();
    }
  };

  const getStepLabels = () => {
    return Object.entries(STEP_CONFIG)
      .filter(([_, config]) => config.order <= stepConfig?.total)
      .sort(([_, a], [__, b]) => a.order - b.order)
      .map(([step, config]) => ({
        step: step as OnboardingStep,
        label: config.label,
        order: config.order,
        isActive: config.order === stepConfig?.order,
        isCompleted: config.order < (stepConfig?.order || 0),
      }));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className={`w-full ${MAX_WIDTH_CLASSES[maxWidth]} mx-auto`}>
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <div className="bg-primary rounded-lg p-2 mr-3">
              <Package className="h-6 w-6 text-primary-foreground" />
            </div>
            <h1 className="text-2xl font-bold text-gray-900">Quildora</h1>
          </div>

          <h2 className="text-xl font-semibold text-gray-900 mb-2">{title}</h2>
          <p className="text-gray-600">{description}</p>
        </div>

        {/* Progress Section */}
        <div className="mb-8">
          {/* Desktop Progress Indicator */}
          <div className="hidden md:block">
            <OnboardingProgressIndicator
              variant="horizontal"
              showLabels={true}
              showStepNumbers={true}
              className="mb-6"
            />
          </div>

          {/* Mobile Progress Indicator */}
          <div className="block md:hidden">
            <OnboardingProgressMobile />
          </div>
        </div>

        {/* Back Button */}
        {showBackButton && (
          <div className="mb-6">
            <Button
              variant="ghost"
              onClick={handleBack}
              className="text-sm p-2 h-auto"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Button>
          </div>
        )}

        {/* Main Content */}
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          {children}
        </div>

        {/* Help Text */}
        <div className="text-center mt-6">
          <p className="text-sm text-gray-500">
            Need help? Contact our support team at{" "}
            <a
              href="mailto:<EMAIL>"
              className="text-primary hover:underline"
            >
              <EMAIL>
            </a>
          </p>
        </div>
      </div>
    </div>
  );
}

// Helper hook for common onboarding layout patterns
export function useOnboardingNavigation() {
  const router = useRouter();

  const navigateToStep = (step: string) => {
    router.push(`/auth/signup/business/${step}`);
  };

  const navigateToJoinFlow = (step?: string) => {
    const path = step ? `/auth/signup/join/${step}` : "/auth/signup/join";
    router.push(path);
  };

  const navigateToWelcome = () => {
    router.push("/auth/welcome");
  };

  const navigateToSignIn = () => {
    router.push("/auth/signin");
  };

  return {
    navigateToStep,
    navigateToJoinFlow,
    navigateToWelcome,
    navigateToSignIn,
  };
}

// Type exports for other components
export type { OnboardingLayoutProps };
